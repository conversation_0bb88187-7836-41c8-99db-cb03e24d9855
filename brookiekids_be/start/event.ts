import Event from '@ioc:Adonis/Core/Event'
import ChatHistory from 'App/Models/ChatHistory'
import { broadcastChat, broadcastHistory } from 'App/Network/ws_service'
import * as admin from 'firebase-admin'
import Device from 'App/Models/Device'
import PushNotification from 'App/Models/PushNotification'
import { MailerSend, EmailParams, Sender, Recipient } from 'mailersend'
import MailerLite from '@mailerlite/mailerlite-nodejs'
import Plan from 'App/Models/Plan'
import stripe from 'stripe'
import User from 'App/Models/User'

if (process.env.NODE_ENV == 'production') {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
  })
}

Event.on('notify:user', async (data) => {
  console.log('notify:user', data)
  try {
    if (data.user_ids.length === 0) {
      return
    }
    const findDeviceTokens = await Device.query().whereIn('user_id', data.user_ids)
    const registrationTokens = findDeviceTokens.map((item) => item.token)
    if (registrationTokens.length === 0) {
      return
    }
    for (let i = 0; i < registrationTokens.length; i += 499) {
      const splicedTokens = registrationTokens.slice(i, i + 499)
      var message = {
        notification: {
          title: data.title,
          body: data.body,
        },
        data: {
          key: `${data.key}`,
          type: `${data?.type ?? ''}`,
          scope: `${data?.scope ?? ''}`,
        },
        android: {
          notification: {
            title: data.title,
            body: data.body,
            sound: 'default',
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: data.title,
                body: data.body,
              },
              contentAvailable: true,
              sound: 'default',
              notification: {
                title: data.title,
                body: data.body,
              },
            },
          },
        },
        tokens: splicedTokens,
      }
      // Send a message to the device corresponding to the provided
      // using await to wait for sending to 400 token
      await admin
        .messaging()
        .sendMulticast(message)
        .then((response) => {
          // Response is a message ID string.
          console.log('Successfully sent message:', response)
          response.responses?.forEach((r) => {
            if (r.error) {
              console.log(r.error)
            }
          })
        })
        .catch((error) => {
          console.log('Error sending message:', error)
        })
    }
  } catch (error) {
    console.log(error)
  }
})

Event.on('send:messages', async (data) => {
  console.log('send:messages', data)
  try {
    const findDeviceTokens = await Device.query()
      .whereIn('user_id', data.user_ids)
      .whereNotNull('token')
    console.log(findDeviceTokens.length)
    const registrationTokens = findDeviceTokens.map((item) => item.token)
    const findPushNotification = await PushNotification.query().where('id', data.id).first()
    if (registrationTokens.length === 0) {
      return
    }
    var message = {
      priority: 'high',
      notification: {
        title: data.title,
        body: data.body,
      },
      data: {
        key: `${data.key}`,
        type: `${data?.type ?? ''}`,
      },
      android: {
        notification: {
          title: data.title,
          body: data.body,
          sound: 'default',
        },
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title: data.title,
              body: data.body,
            },
            contentAvailable: true,
            sound: 'default',
            notification: {
              title: data.title,
              body: data.body,
            },
          },
        },
      },
      tokens: registrationTokens,
    }
    if (data?.image_url !== '') {
      message['notification']['image'] = data.image_url
    }

    // Send a message to the device corresponding to the provided
    // registration token.
    await admin
      .messaging()
      .sendEachForMulticast(message)
      .then((response) => {
        // Response is a message ID string.
        console.log('Successfully sent message:', response)
        let hasError = false
        response.responses?.forEach((r) => {
          if (r.error) {
            console.log(r.error)
            hasError = true
          }
        })

        if (!hasError) {
          if (findPushNotification) findPushNotification.sent = true
        }
      })
      .catch((error) => {
        console.log('Error sending message:', error)
      })
    if (findPushNotification) await findPushNotification.save()
  } catch (error) {
    console.log(error)
  }
})

Event.on('auth:code', async (data) => {
  console.log('auth:code', data.email, data.code)
  const mailersendToken = process.env.MAILERSEND_TOKEN

  if (!mailersendToken) {
    // handle error
    return
  }

  const mailerSend = new MailerSend({
    apiKey: mailersendToken,
  })
  const sentFrom = new Sender('<EMAIL>', 'BrookieKids')
  const recipients = [new Recipient(data.email)]
  const personalization = [
    {
      email: data.email,
      data: {
        code: data.code,
        name: data.email,
        account_name: 'Brookie Kids',
        support_email: '<EMAIL>',
      },
    },
  ]
  // const variables = [
  //   {
  //     email: data.email,
  //     substitutions: [
  //       {
  //         var: 'name',
  //         value: data.email,
  //       },
  //       {
  //         var: 'code',
  //         value: data.code,
  //       },
  //       {
  //         var: 'account_name',
  //         value: 'Brookie Kids',
  //       },
  //       {
  //         var: 'support_email',
  //         value: '<EMAIL>',
  //       },
  //     ],
  //   },
  // ]

  const emailParams = new EmailParams()
    .setFrom(sentFrom)
    .setTo(recipients)
    .setReplyTo(sentFrom)
    // .setVariables(variables)
    .setPersonalization(personalization)
    .setSubject('Login to BrookieKids')
    .setTemplateId('x2p03478q9ylzdrn')

  await mailerSend.email.send(emailParams)
})

Event.on('auth:register', async (data) => {
  console.log('auth:register', data.email, data.group)
  const mailerliteToken = process.env.MAILERLITE_API

  if (!mailerliteToken) {
    // handle error
    console.log('auth:register token missing')
    return
  }

  try {
    // use mailerlite to send email
    const mailerlite = new MailerLite({
      api_key: mailerliteToken,
    })

    mailerlite.subscribers
      .createOrUpdate({
        email: data.email,
        groups: [data.group],
      })
      .then((response) => {
        console.log('auth:register status:', response.status)
      })
      .catch((error) => {
        if (error.response) console.log('auth:register error: ', error.response.data)
      })
    // await axios.post(
    //   'https://connect.mailerlite.com/api/subscribers',
    //   {
    //     email: data.email,
    //     groups: [data.group],
    //   },
    //   {
    //     headers: {
    //       Authorization: `Bearer ${mailerliteToken}`,
    //     },
    //   }
    // )
  } catch (error) {
    console.log(error)
  }
})

Event.on('chat:create', async (chatId: number) => {
  console.log('chat:create', chatId)
  try {
    broadcastChat(chatId)
  } catch (e) {
    console.log('error while broadcast on chat creation', e)
  }
})

Event.on('history:create', async (historyId: number) => {
  console.log('history:create', historyId)
  try {
    broadcastHistory(historyId)
  } catch (e) {
    console.log('error while broadcast on chat history creation', e)
  }
})

Event.on('chat:seen', async (data: { historyIds: number[]; chatId: number }) => {
  console.log('chat:seen', data)
  const datetime = new Date()
  const upadatedRows = await ChatHistory.query().whereIn('id', data.historyIds).andWhereNull('seen')

  if (upadatedRows.length > 0) {
    await ChatHistory.query().whereIn('id', data.historyIds).andWhereNull('seen').update({
      seen: datetime,
    })
    Event.emit('chat:create', data.chatId)
  }
})

Event.on('webhook:wix', async (data: { email: string }) => {
  console.log('webhook:wix', data.email)
  const mailerliteToken = process.env.MAILERLITE_WEBHOOK

  if (!mailerliteToken) {
    // handle error
    console.log('webhook:wix token missing')
    return
  }
  try {
    const mailerlite = new MailerLite({ api_key: mailerliteToken })

    mailerlite.subscribers
      .createOrUpdate({
        email: data.email,
        groups: [
          '91460863081318162', //Activity packs shipped out (jun 2023 onwards)
          // '92030599157515333', //Testing TYY
        ],
      })
      .then((response) => {
        console.log('webhook:wix status:', response.status)
      })
      .catch((error) => {
        if (error.response) console.log('webhook:wix error: ', error.response.data)
      })

    // await axios.post(
    //   'https://connect.mailerlite.com/api/subscribers',
    //   {
    //     email: data.email,
    //     groups: [
    //       '91460863081318162', //Activity packs shipped out (jun 2023 onwards)
    //       // '92030599157515333', //Testing TYY
    //     ],
    //   },
    //   {
    //     headers: {
    //       Authorization: `Bearer ${mailerliteToken}`,
    //     },
    //   }
    // )
  } catch (error) {
    console.log(error)
  }
})

Event.on('activity:start', async (data: { email: string }) => {
  const mailerliteToken = process.env.MAILERLITE_API

  if (!mailerliteToken) {
    // handle error
    console.log('activity:start token missing')
    return
  }

  try {
    const mailerlite = new MailerLite({
      api_key: mailerliteToken,
    })

    mailerlite.subscribers
      .createOrUpdate({
        email: data.email,
        groups: [],
      })
      .then((response) => {
        console.log('activity:start status:', response.status)
      })
      .catch((error) => {
        if (error.response) console.log('activity:start error: ', error.response.data)
      })
  } catch (error) {
    console.log(error)
  }
})

Event.on('plan:trial', async (data) => {
  console.log('plan:trial', data.email)

  const plan = await Plan.find(1)

  if (!plan) {
    console.log('plan:trial', 'plan not found')
    return
  }

  await plan.load('pricings')

  if ((plan.pricings?.length ?? 0) == 0) {
    console.log('plan:trial', 'plan pricing not found')
    return
  }

  try {
    const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)

    // 1. Create or retrieve customer
    let customer: stripe.Customer
    let customerList = await stripeInstance.customers.list({ email: data.email, limit: 1 })

    if (customerList.data.length === 0) {
      // Create new customer with just email
      customer = await stripeInstance.customers.create({ email: data.email })
      // will trigger onCustomerCreated webhook
      console.log('plan:trial', 'customer created', customer.id)
    } else {
      customer = customerList.data[0]
      console.log('plan:trial', 'customer found', customer.id)
      // check if customer exist in our database
      const findCustomer = await User.query().where('stripe_customer_id', customer.id).first()
      if (!findCustomer) {
        // assume the stripe customer is invalid, create a new one
        customer = await stripeInstance.customers.create({ email: data.email })
      }
    }

    // 2. Create subscription with trial
    const subscription = await stripeInstance.subscriptions.create({
      customer: customer.id,
      items: [{ price: plan.pricings[0].stripePriceId! }],
      trial_period_days: 7,
      // Don't collect payment method until trial ends
      collection_method: 'charge_automatically',
      trial_settings: {
        end_behavior: {
          missing_payment_method: 'cancel', // Cancel if no payment method added by trial end
        },
      },
    })

    console.log('plan:trial', 'subscription created', subscription.id)
  } catch (error) {
    console.log(error)
  }
})

Event.on('stripe:product-created', 'Plan.onStripeProductCreated')
Event.on('stripe:price-created', 'Plan.onStripePriceCreated')
Event.on('stripe:product-updated', 'Plan.onStripeProductUpdated')
Event.on('stripe:price-updated', 'Plan.onStripePriceUpdated')
Event.on('stripe:product-deleted', 'Plan.onStripeProductDeleted')
Event.on('stripe:price-deleted', 'Plan.onStripePriceDeleted')
Event.on('stripe:customer-created', 'Plan.onCustomerCreated')
Event.on('stripe:customer-updated', 'Plan.onCustomerUpdated')
Event.on('stripe:subscription-created', 'Plan.onSubscriptionCreated')
Event.on('stripe:subscription-updated', 'Plan.onSubscriptionUpdated')
Event.on('stripe:subscription-deleted', 'Plan.onSubscriptionDeleted')
Event.on('stripe:plan-purchased', 'Plan.onPurchased')
Event.on('stripe:plan-failed', 'Plan.onPaymentFailed')