/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes.ts` as follows
|
| import './routes/cart'
| import './routes/customer'
|
*/

import Route from '@ioc:Adonis/Core/Route'

Route.get('/success', async () => {
  return { success: true }
})
Route.get('/cancel', async () => {
  return { success: false, message: 'Customer cancelled' }
})

Route.group(() => {
  Route.get('/activities', 'ActivitiesController.find')
  Route.get('/activities/:id', 'ActivitiesController.findOne')
  Route.get('/plan/:plan_id/activities', 'ActivitiesController.findInPlan')
  Route.get('/tags', 'TagsController.index')
  Route.post('/webhook/wix', 'WebhooksController.wixCallback')
  Route.post('/webhooks/stripe', 'WebhooksController.stripeCallback')

  Route.group(() => {
    Route.post('/purchased', 'PlansController.onPurchased')
    Route.post('/cycle-started', 'PlansController.onCycleStarted')
    Route.post('/cancelled', 'PlansController.onCancelled')
    Route.post('/ended', 'PlansController.onEnded')
  }).prefix('/webhook/plans')
}).prefix('/api/v1')

Route.group(() => {
  Route.post('/auth/login', 'AuthController.login').middleware('checkIsAdmin')
}).prefix('/api/v1/admin')

Route.group(() => {
  Route.post('/upload', 'FilesController.uploadFile')

  // users
  Route.get('/users', 'UsersController.find')

  // children
  Route.get('/user/:user_id/children', 'ChildrenController.findChildren')

  // credit
  Route.get('/credits', 'CreditsController.findAll')
  Route.put('/credits/:id', 'CreditsController.update')

  // transactions
  Route.get('/transactions', 'WalletsController.findTransactions')
  Route.post('/free-credits', 'WalletsController.freeCredits')

  // packs
  Route.get('/packs', 'PacksController.adminFind')
  Route.get('/packs/:id', 'PacksController.adminFindOne')
  Route.put('/packs/create', 'PacksController.create')
  Route.put('/packs/update/:id', 'PacksController.update')
  Route.put('/pack-slides/update/:id', 'PacksController.updatePackSlide')

  // pack level
  Route.put('/pack-level-audio/update/:id', 'PackLevelsController.updatePackLevelAudio')
  Route.put('/pack-level-rive/update/:id', 'PackLevelsController.updatePackLevelRive')

  // pack codes
  Route.get('/pack-codes', 'PackCodesController.find')
  Route.post('/pack-codes/create', 'PackCodesController.create')

  // voucher codes
  Route.get('/voucher-codes', 'VouchersController.findAll')
  Route.post('/voucher-codes/create', 'VouchersController.create')

  // stories
  Route.get('/stories', 'StoriesController.adminFind')
  Route.get('/stories/:id', 'StoriesController.adminFindOne')
  Route.get('/stories/pack/:pack_id/level/:level', 'StoriesController.findStoriesByPackAndLevel')
  Route.put('/stories/create', 'StoriesController.create')
  Route.put('/stories/update/:id', 'StoriesController.update')
  Route.get('/stories/redemption/:id', 'StoryOrdersController.findUserStoryRedemption')
  Route.post('/community-stories/ordering', 'StoriesController.communityStoriesSorting')
  // Route.post('/bundle-stories/ordering', 'StoriesController.bundleStoriesSorting')

  // tags
  Route.get('/tags', 'TagsController.adminIndex')
  Route.post('/tags', 'TagsController.store')
  Route.put('/tags/:id', 'TagsController.update')
  Route.delete('/tags/:id', 'TagsController.destroy')

  // chapters
  Route.get('/chapters/pack/:pack_id/level/:level', 'ChaptersController.findChaptersByPackAndLevel')
  Route.get('/chapters/story/:story_id', 'ChaptersController.findChaptersByStoryId')
  Route.get('/chapters', 'ChaptersController.findAll')
  Route.post('/chapters/create', 'ChaptersController.create')
  Route.put('/chapters/update/:id', 'ChaptersController.update')

  // recordings
  Route.get('/recordings', 'RecordingResultsController.find')

  // Route.post('/upload', 'FilesController.uploadFile').middleware('auth')

  // chats
  Route.get('/chats', 'AdminChatsController.find')
  Route.get('/chats/:id', 'AdminChatsController.findOne')
  Route.post('/chats', 'AdminChatsController.create')
  Route.post('/chats/:id/histories', 'AdminChatsController.createHistory')
  Route.get('/chats/:id/histories', 'AdminChatsController.findHistories')

  // notifications
  Route.get('/notifications', 'PushNotificationsController.find')
  Route.post('/notifications/create', 'PushNotificationsController.sendAppNotification')

  // versions
  Route.get('/versions', 'VersionsController.find')
  Route.post('/versions/create', 'VersionsController.create')
  Route.put('/versions/update/:id', 'VersionsController.update')

  Route.post('/reports', 'ReportsController.create')

  // Route.get('/active-pack-codes', 'PackCodesController.findActivePackCodes')
  Route.get('/pack-codes-report', 'ReportsController.generatePackCodesReport')
  Route.get('/story-ratings-report', 'ReportsController.generateStoryRatingsReport')
  Route.get('/transactions-report', 'ReportsController.generateTransactionsReport')

  // story ratings
  Route.get('/story-ratings', 'StoryRatingsController.findStoryRatings')

  // preschool
  Route.get('/preschools', 'PreschoolsController.adminFind')
  Route.post('/preschools', 'PreschoolsController.create')
  Route.put('/preschools/:id', 'PreschoolsController.update')
  Route.get('/preschools/redemption/:id', 'StoryOrdersController.findUserPreschoolRedemption')
  Route.post('/preschools/block-user', 'StoryOrdersController.updateUserPreschoolBlocked')

  // bundle
  Route.get('/bundles', 'BundlesController.adminFind')
  Route.post('/bundles', 'BundlesController.create')
  Route.post('/bundles/:id/add-stories', 'BundlesController.addStoriesToBundle')
  Route.delete('/bundles/:id/remove-story/:story_id', 'BundlesController.removeStoryFromBundle')
  Route.put('/bundles/:id', 'BundlesController.update')
  Route.get('/bundles/:bundle_id/redemption', 'BundlesController.findBundleRedemption')

  // plans
  Route.get('/plans', 'PlansController.adminFind')
  Route.post('/plans', 'PlansController.create')
  Route.put('/plans/:id', 'PlansController.update')
  // Route.post('/plans/:id/stories', 'PlansController.updatePlanStories')
  Route.post('/plans/:id/stories', 'PlansController.addStoriesToPlan')
  Route.delete('/plans/:id/stories/:story_id', 'PlansController.removeStoryFromPlan')
  Route.put('/plan-pricings/:id', 'PlansController.updatePlanPricing')

  // Route.post('/plans/block-user', 'PlansController.updateSubscriptionBlocked')

  Route.get('/plans/:id/stories', 'PlansController.adminFindPlanStories')
  Route.get('/plans/:id/stories/all', 'PlansController.adminFindAllPlanStories')
  Route.post('/plans/:id/stories/ordering', 'PlansController.planStoriesSorting')
  Route.post('/plans/:id/stories/levels', 'PlansController.updatePlanStoriesLevel')
  Route.post('/plans/:id/stories/featured', 'PlansController.updatePlanStoriesFeatured')
  Route.post('/plans/:id/stories/free', 'PlansController.updatePlanStoriesFree')

  // settings
  Route.get('/system-settings', 'SettingsController.find')
  Route.get('/system-settings/:id', 'SettingsController.findOne')
  Route.post('/system-settings', 'SettingsController.create')
  Route.put('/system-settings/:id', 'SettingsController.update')
  Route.get('/system-settings/handle/:handle', 'SettingsController.findByHandle')
})
  .prefix('/api/v1/admin')
  .middleware('auth')

Route.group(() => {
  Route.get('/', 'UsersController.findMyself')
  Route.put('/', 'UsersController.updateUserInformation')
  Route.delete('/', 'UsersController.deleteMe')

  Route.get('/wallet', 'WalletsController.findMyWallet')
  Route.get('/wallet-transactions', 'WalletsController.findMyTransactions')

  Route.get('/children', 'ChildrenController.findMyChildren')
  Route.post('/children', 'ChildrenController.addChild')
  Route.put('/children/:id', 'ChildrenController.editChild')
  Route.delete('/children/:id', 'ChildrenController.removeChild')

  Route.get('/packs', 'PacksController.findMyPacks')
  Route.post('/packs', 'PacksController.redeemMyPack')
  Route.get('/packs/:id/user-pack', 'PacksController.findUserPack')
  Route.get('/user-packs', 'PacksController.findUserPacks')
  Route.put('/user-packs/:id', 'PacksController.updateUserPack')

  Route.get('/stories', 'StoriesController.findMyStories')
  Route.get('/stories/id', 'StoriesController.findStory')
  Route.get('/stories/code/:qrCode', 'StoriesController.findStoryByQrCode')

  Route.post('/auth/refresh-token', 'AuthController.refreshToken')

  Route.post('/recordings', 'RecordingsController.store')
  Route.post('/recordings/poll', 'RecordingsController.find')

  Route.post('/sessions', 'SessionsController.create')
  Route.post('/sessions/:id/end', 'SessionsController.endSession')

  Route.get('/chats', 'UserChatsController.find')
  Route.get('/chats/:id', 'UserChatsController.findOne')
  Route.post('/chats', 'UserChatsController.create')
  Route.post('/chats/:id/histories', 'UserChatsController.createHistory')
  Route.get('/chats/:id/histories', 'UserChatsController.findHistories')

  Route.post('/devices', 'DevicesController.createMyToken')

  Route.post('/purchase-click', 'PurchaseClickTrackersController.clickTracker')

  Route.put('/packs/:id/level-completed-prompt', 'PacksController.updateLevelCompletedPrompt')

  // story rating
  Route.get('/stories/:story_id/story-ratings', 'StoryRatingsController.findMyStoryRating')
  Route.put('/story-ratings/:id', 'StoryRatingsController.updateMyStoryRating')

  // user setting
  Route.get('/user-settings', 'UserSettingsController.findUserSetting')
  Route.put('/user-settings/:id', 'UserSettingsController.updateUserSetting')

  // plans/checkout/subscriptions
  Route.post('/checkout', 'PlansController.createStripeCheckout')
  Route.get('/subscriptions', 'PlansController.findMySubscriptions')
  Route.get('/subscriptions/:id', 'PlansController.findMySubscription')
  Route.get('/plans/:id/subscriptions', 'PlansController.findMySubscriptionWithPlan')
  Route.get('/story-orders/:story_id', 'PlansController.findMyStoryOrder')
})
  .prefix('/api/v1/me')
  .middleware('auth')

// public api
Route.group(() => {
  Route.group(() => {
    // Route.get('/', 'VersionsController.find')
    // Route.get('/:id', 'VersionsController.findOne')
    Route.get('/latest', 'VersionsController.findLatestVersionPlatform')
  }).prefix('/versions')

  Route.post('/auth/magic-token', 'AuthController.sendAuthCode')
  Route.post('/auth/magic-login', 'AuthController.loginMagic')
  Route.post('/auth/apple', 'AuthController.loginApple')
  Route.post('/auth/google', 'AuthController.loginGoogle')

  // silent auth
  Route.get('/packs/:id', 'PacksController.findOne').middleware('silentAuth')
  Route.get('/packs', 'PacksController.findAll').middleware('silentAuth')
  Route.get('/stories', 'StoriesController.findAll').middleware('silentAuth')
  Route.get('/stories/:id', 'StoriesController.findOne').middleware('silentAuth')
  Route.get('/packs/handle/:qr_code', 'PacksController.findPackByQrCode').middleware('silentAuth')
  Route.get('/stories/handle/:handle', 'StoriesController.findStoryByHandle').middleware(
    'silentAuth'
  )

  // instant app
  Route.get('/instant/stories/:id', 'StoriesController.instantFindStory')
  Route.post('/instant/recordings', 'RecordingsController.instantStore')
  Route.post('/instant/recordings/poll', 'RecordingsController.instantFind')

  // require login
  // Route.post('/app-purchase-callback', 'PaymentsController.callback')
  Route.post('/voucher/verify', 'VouchersController.verify').middleware('requireLogin')
  Route.post('/app-purchase/waived', 'PaymentsController.waivedPurchase').middleware('requireLogin')
  Route.post('/app-purchase/android', 'PaymentsController.validatePurchaseAndroid').middleware(
    'requireLogin'
  )
  Route.post('/app-purchase/ios', 'PaymentsController.validatePurchaseIOS').middleware(
    'requireLogin'
  )
  Route.post(
    '/app-purchase/credits/android',
    'PaymentsController.validatePurchaseCreditsAndroid'
  ).middleware('requireLogin')
  Route.post(
    '/app-purchase/credits/ios',
    'PaymentsController.validatePurchaseCreditsIOS'
  ).middleware('requireLogin')
  Route.post('/story-purchase', 'PaymentsController.validatePurchaseWithCredit').middleware(
    'requireLogin'
  )

  // redirect
  Route.get('/redirect', 'RedirectsController.redirect')

  // credits
  Route.get('/credits', 'CreditsController.findAll')

  // preschools
  Route.get('/preschools', 'PreschoolsController.findAll').middleware('silentAuth')

  // bundle
  Route.get('/bundles', 'BundlesController.findAll').middleware('silentAuth')

  // plans
  Route.get('/plans', 'PlansController.findPlans').middleware('silentAuth')
  Route.get('/plans/:id', 'PlansController.findOnePlan').middleware('silentAuth')
  Route.get('/plans/:id/stories', 'PlansController.findPlanStories').middleware('silentAuth')

  // settings
  Route.get('/system-settings/handle/:handle', 'SettingsController.findByHandle')
}).prefix('/api/v1')

Route.group(() => {
  // Route.post('/pack-codes', 'PackCodesController.findActivePackCodes')
  // Route.post('/pack-codes-report', 'ReportsController.generatePackCodesReport')
  // Route.post('/story-ratings-report', 'ReportsController.generateStoryRatingsReport')
  // Route.post('/transactions-report', 'ReportsController.generateTransactionsReport')
})
  .prefix('/api/v1/private')
  .middleware('checkIsPrivate')
