{"typescript": true, "commands": ["./commands", "@adonisjs/core/build/commands/index.js", "@adonisjs/repl/build/commands", "@adonisjs/lucid/build/commands", "adonis-lucid-filter/build/commands"], "exceptionHandlerNamespace": "App/Exceptions/Handler", "aliases": {"App": "app", "Config": "config", "Database": "database", "Contracts": "contracts"}, "preloads": ["./start/routes", "./start/kernel", {"file": "./start/cron", "environment": ["web"]}, {"file": "./start/event", "environment": ["web"]}, {"file": "./start/websocket_server", "environment": ["web"]}, "./start/bull"], "providers": ["./providers/AppProvider", "./providers/BullProvider", "@adonisjs/core", "@adonisjs/auth", "@adonisjs/lucid", "adonis-lucid-filter", "@adonisjs/lucid-slugify", "@adonisjs/drive-s3", "@adonisjs/redis"], "aceProviders": ["@adonisjs/repl"], "tests": {"suites": [{"name": "functional", "files": ["tests/functional/**/*.spec(.ts|.js)"], "timeout": 60000}]}, "testProviders": ["@japa/preset-adonis/TestsProvider"]}