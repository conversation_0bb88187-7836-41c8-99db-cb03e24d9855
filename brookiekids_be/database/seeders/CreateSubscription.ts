import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Plan from 'App/Models/Plan'
import PlanPricing from 'App/Models/PlanPricing'
import StoryOrder from 'App/Models/StoryOrder'
import Subscription from 'App/Models/Subscription'
import User from 'App/Models/User'
import { DateTime } from 'luxon'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const janUsers = ['<EMAIL>'] // 27/1/2026
    // const decUsers = ['<EMAIL>'] // 31/12/2025

    const plan = await Plan.findByOrFail('id', 1)
    const planPricing = await PlanPricing.findByOrFail('id', 3)
    await plan.load('planStories')

    const userList = await User.query().whereIn('email', janUsers)
    console.log(userList.length)
    for (const user of userList) {
      await Subscription.create({
        planId: plan.id,
        planPricingId: planPricing.id,
        provider: 'stripe',
        providerPlanId: planPricing.stripePriceId!,
        // todo: providerSubscriptionId
        userId: user.id,
        startDate: DateTime.now(),
        // endDate: DateTime.fromFormat('2024-12-31', 'yyyy-MM-dd'),
        cycleStartDate: DateTime.now(),
        cycleEndDate: DateTime.fromFormat('2026-01-27', 'yyyy-MM-dd'),
        status: 'active',
      })

      for (const story of plan.planStories) {
        // update or create StoryOrder for every story in this plan if user doesn't have it
        await StoryOrder.updateOrCreate(
          {
            storyId: story.id,
            userId: user.id,
          },
          {
            storyId: story.id,
            userId: user.id,
            blocked: false,
          }
        )
      }
    }
  }
}
