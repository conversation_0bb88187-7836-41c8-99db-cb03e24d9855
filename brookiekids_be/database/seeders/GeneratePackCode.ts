import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Pack from 'App/Models/Pack'
import PackCode from 'App/Models/PackCode'
import Randomstring from 'randomstring'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const CODES_PER_PACK = 50
    const packs = await Pack.query()
    for (let pack of packs) {
      for (let i = 1; i <= CODES_PER_PACK; i++) {
        let uid: string
        let isUnique = true
        do {
          uid = Randomstring.generate(6)
          const checkCode = await PackCode.findBy('pack_code', uid)
          if (checkCode) {
            isUnique = false
          } else isUnique = true
        } while (!isUnique)

        await PackCode.create({
          packId: pack.id,
          packCode: uid,
        })
      }
    }
  }
}
