import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'plans'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('appstore_product_id')
      table.string('playstore_product_id')
      table.string('stripe_product_id')
      table.string('stripe_price_id')
      table.string('title')
      table.string('handle')
      table.text('description')
      table.json('region')
      table.string('language')
      table.boolean('blocked').defaultTo(false)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
