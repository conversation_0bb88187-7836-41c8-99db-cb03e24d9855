import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'story_orders'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('voucher_id').unsigned().references('id').inTable('vouchers')
      table.integer('story_id').unsigned().references('id').inTable('stories')
      table.integer('user_id').unsigned().references('id').inTable('users')
      table.decimal('final_amount', 8, 2)
      table.decimal('discount_amount', 8, 2)
      table.json('story_statuses')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
