import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('preschool_id').unsigned().references('id').inTable('preschools')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('preschool_id')
      table.dropColumn('preschool_id')
    })
  }
}
