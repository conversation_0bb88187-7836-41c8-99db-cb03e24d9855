import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'recording_results'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('category')
      table.string('extra_text')
      table.string('remaining_text')
      table.string('pinyin_hypothesis')
      table.string('hypothesis')
      table.float('hypothesis_score')
      table.string('language')
      table.string('device')
      table.string('minimum_volume')
      table.json('raw_result')
      table.json('calibrations')
      table.json('volumes')
      table
        .integer('chapter_id')
        .unsigned()
        .references('id')
        .inTable('chapters')
        .onDelete('CASCADE')
      table.integer('story_id').unsigned().references('id').inTable('stories').onDelete('CASCADE')
      table.integer('file_id').unsigned().references('id').inTable('files').onDelete('CASCADE')
      table.integer('user_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      table
        .integer('session_id')
        .unsigned()
        .references('id')
        .inTable('sessions')
        .onDelete('CASCADE')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
