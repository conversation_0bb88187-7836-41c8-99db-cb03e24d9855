import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'chat_histories'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table
        .integer('chat_id')
        .unsigned()
        .references('id')
        .inTable('chats')
        .nullable()
        .defaultTo(null)
        .onDelete('SET NULL')

      table
        .integer('user_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .nullable()
        .defaultTo(null)
        .onDelete('SET NULL')

      table.boolean('is_deleted').defaultTo(false)

      table.timestamp('seen', { useTz: true })
      table.text('content', 'longtext')
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
