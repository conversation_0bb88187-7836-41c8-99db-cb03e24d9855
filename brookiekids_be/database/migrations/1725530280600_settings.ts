import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'settings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('handle').notNullable().unique()
      table.string('value')
      table.string('status')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('handle')
      table.dropColumn('value')
      table.dropColumn('status')
    })
  }
}
