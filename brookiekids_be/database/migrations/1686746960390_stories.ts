import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('word_count')
      table.decimal('price', 8, 2)
      table.string('status').defaultTo('active')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('word_count')
    })
  }
}
