import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'plans'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumns('appstore_product_id', 'playstore_product_id', 'stripe_price_id', 'price')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('appstore_product_id')
      table.string('playstore_product_id')
      table.string('stripe_price_id')
      table.decimal('price', 8, 2)
    })
  }
}
