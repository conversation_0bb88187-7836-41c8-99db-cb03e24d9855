import BaseSchema from '@ioc:Adonis/Lucid/Schema'
import { StoryRatingStatus } from 'App/Models/StoryRating'
import _ from 'lodash'

export default class extends BaseSchema {
  protected tableName = 'story_ratings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('user_id').unsigned().references('id').inTable('users')
      table.integer('story_id').unsigned().references('id').inTable('stories')
      table.integer('rating')
      table.text('review')
      table.integer('count').defaultTo(1)
      table.enum('status', _.values(StoryRatingStatus)).defaultTo(StoryRatingStatus.ASKING)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
