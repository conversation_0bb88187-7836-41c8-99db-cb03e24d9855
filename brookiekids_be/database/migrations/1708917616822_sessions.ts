import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'sessions'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('game_score').defaultTo(0)
      table.integer('full_game_score').defaultTo(0)
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, table => {
      table.dropColumn('game_score')
      table.dropColumn('full_game_score')
    })
  }
}
