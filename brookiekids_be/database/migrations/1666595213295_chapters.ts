import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'chapters'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('title')
      table.string('handle')
      table.text('description')
      table.boolean('is_free_response').defaultTo(false)
      table.json('options')
      table.integer('story_id').unsigned().references('id').inTable('stories').onDelete('SET NULL')
      table.integer('file_id').unsigned().references('id').inTable('files').onDelete('SET NULL')
      table
        .integer('fallback_chapter_id')
        .unsigned()
        .references('id')
        .inTable('chapters')
        .onDelete('SET NULL')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
