import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import { v4 as uuid } from 'uuid'
import axios from 'axios'
import fs from 'fs'
import { uploadToS3Bucket } from './FilesController'
import AWS from 'aws-sdk'
import File from 'App/Models/File'
import _, { uniq } from 'lodash'
import { DateTime } from 'luxon'
import RecordingResult from 'App/Models/RecordingResult'
import AzureAuth from 'App/Models/AzureAuth'
import {
  SpeechConfig,
  AudioConfig,
  SpeechRecognizer,
  PronunciationAssessmentResult,
  PronunciationAssessmentConfig,
  PronunciationAssessmentGradingSystem,
} from 'microsoft-cognitiveservices-speech-sdk'
import util from 'util'
import { exec } from 'child_process'
import pinyin from 'pinyin'
import { RecognitionStatus } from 'microsoft-cognitiveservices-speech-sdk/distrib/lib/src/common.speech/Exports'
import Story, { StoryType } from 'App/Models/Story'
import Session from 'App/Models/Session'
import Chapter, { Option } from 'App/Models/Chapter'
import Database from '@ioc:Adonis/Lucid/Database'

const awaitExec = util.promisify(exec)

export const uploadStreamToS3Bucket = async (
  stream: any,
  bucket: string,
  folder: string,
  userProfile?: string
): Promise<{ key: string; url: string }> => {
  const s3 = new AWS.S3({
    region: process.env.S3_REGION,
    secretAccessKey: process.env.S3_SECRET,
    accessKeyId: process.env.S3_KEY,
  })

  try {
    let mimeType = 'image' + '/' + 'jpg'
    const name = userProfile
      ? folder + '/' + userProfile + '-' + uuid() + '.' + '.jpg'
      : folder + '/' + uuid() + '.' + '.jpg'

    await s3
      .upload({
        Key: name,
        Bucket: bucket,
        ContentType: mimeType,
        Body: stream,
        ACL: 'public-read',
      })
      .promise()
    let url = `https://${process.env.S3_BUCKET}.s3.amazonaws.com/${name}`
    return {
      key: name,
      url,
    }
  } catch (err) {
    console.log(err)
    return err
  }
}

const decodeHanziToHYPY = async (azureResult, answer) => {
  let toCompare = [...answer]
  const pinyinResult = await pinyin(azureResult, { style: pinyin.STYLE_NORMAL })
  let extraWords: any[] = []

  // console.log(pinyinResult)
  pinyinResult.forEach((recordedWords) => {
    recordedWords.forEach((word) => {
      if (toCompare.includes(word)) {
        toCompare = _.pull(toCompare, word)
      } else {
        extraWords.push(word)
      }
    })
  })
  extraWords.pop()

  // Wanted to ask why does .at() method not work; Got this error >>> Property 'at' does not exist on type 'any[]'
  // console.log(extraWords.at(-1))
  // {extraWords?.at(-1) === "。" ? extraWords = _.pull(extraWords, "。") : null}
  const count = answer.length - toCompare.length
  // const totalCount = answer.length + extraWords.length
  const score = (count / answer.length) * 100

  return { score, toCompare, extraWords, pinyinResult }
}

export default class RecordingsController {
  // chinese (azure + pinyin) + english azure
  public async store({ request, response, auth }: HttpContextContract) {
    const user = await auth.authenticate()

    try {
      const validationSchema = schema.create({
        file: schema.file({
          size: '200mb',
          extnames: ['wav'],
        }),
        category: schema.array().members(schema.string.optional()),
        device: schema.string.optional(),
        user_token: schema.string.optional(),
        language: schema.string(),
        chapter_id: schema.number.optional(),
        story_id: schema.number.optional(),
        session_id: schema.number.optional(),
        calibration: schema.array().members(schema.string()),
        minimum_volume: schema.string(),
        actual_volume: schema.array().members(schema.string()),
        is_repeat: schema.boolean.optional(),
      })

      const validationData = await request.validate({
        schema: validationSchema,
      })

      validationData.language = validationData.language.replace('_', '-')

      const uploadBucket = await uploadToS3Bucket(
        validationData.file,
        process.env.S3_bucket ?? 'hummusedu',
        user.email
      )

      // upload to s3 for backup
      const recording = await File.create({
        src: uploadBucket.url,
        type: 'recording',
        userId: user.id,
      })

      let azureAuth = await AzureAuth.query().where('status', '=', 1).first()
      if (azureAuth === null) {
        const headers = {
          headers: {
            'Ocp-Apim-Subscription-Key': '0e8cacdd8b6f482eac6f698f70f7b8e5',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }

        const tokenResponse = await axios.post(
          `https://southeastasia.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
          null,
          headers
        )

        azureAuth = await AzureAuth.create({
          token: tokenResponse.data,
          status: 0,
        })
      }

      const speechConfig = SpeechConfig.fromAuthorizationToken(
        azureAuth.token,
        process.env.AZURE_REGION ?? 'southeastasia'
      )
      speechConfig.outputFormat = 1
      speechConfig.speechRecognitionLanguage = validationData.language

      const fileName = `${uuid()}.${validationData.file.extname}`
      const soxFile = `/tmp/${fileName}`

      // for ios app
      // post process file buffer to fix fmt missing
      try {
        const { stdout, stderr } = await awaitExec(`sox ${validationData.file.tmpPath} ${soxFile}`)

        if (stderr) {
          console.log(`stderr: ${stderr}`)
        }

        console.log(`stdout: ${stdout}`)
      } catch (error) {
        console.log(`error: ${error.message}`)
      }

      console.log('Language: ', validationData.language)
      const audioConfig = AudioConfig.fromWavFileInput(fs.readFileSync(`${soxFile}`))
      const recordingResults: any[] = []
      const tests: any[] = []
      const uniqueCategories = uniq(validationData.category)

      if (validationData.language.startsWith('zh')) {
        const recognizer = new SpeechRecognizer(speechConfig, audioConfig)
        await new Promise((resolve, reject) => {
          try {
            recognizer.recognizeOnceAsync(async (result) => {
              await uniqueCategories.forEach(async (element) => {
                if (element !== null) {
                  const { score, toCompare, extraWords, pinyinResult } = await decodeHanziToHYPY(
                    result.text,
                    element?.split(' ')
                  )

                  tests.push({
                    raw_result: result,
                    file_id: recording.id,
                    category: element,
                    pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
                    hypothesis: result.text,
                    hypothesis_score: score,
                    remaining_text: toCompare,
                    extra_text: extraWords ?? [],
                    language: validationData.language,
                    user_id: user.id,
                    chapter_id: validationData.chapter_id,
                    story_id: validationData.story_id,
                    session_id: validationData.session_id,
                    device: validationData.device,
                    calibrations: validationData.calibration,
                    volumes: validationData.actual_volume,
                    minimum_volume: validationData.minimum_volume,
                  })
                  recordingResults.push({
                    raw_result: result,
                    file_id: recording.id,
                    category: element,
                    pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
                    hypothesis: result.text,
                    hypothesis_score: score,
                    remaining_text: toCompare,
                    extra_text: extraWords ?? [],
                    language: validationData.language,
                    user_id: user.id,
                    chapter_id: validationData.chapter_id,
                    story_id: validationData.story_id,
                    session_id: validationData.session_id,
                    device: validationData.device,
                    calibrations: validationData.calibration,
                    volumes: validationData.actual_volume,
                    minimum_volume: validationData.minimum_volume,
                    isRepeat: validationData.is_repeat ?? false,
                  })
                }
              })

              recognizer.close()

              resolve(tests)
            })
          } catch (error) {
            console.log(error)
            reject({ score: 0, remaining: [] })
          }
        })
      } else {
        for (const referenceText of uniqueCategories) {
          if (referenceText) {
            console.log('Reference: ', referenceText)
            const recognizer = new SpeechRecognizer(speechConfig, audioConfig)
            const isFreeResponse = referenceText == 'free response'
            let pronunciationConfig
            if (!isFreeResponse) {
              pronunciationConfig = new PronunciationAssessmentConfig(
                referenceText,
                PronunciationAssessmentGradingSystem.HundredMark
              )
              pronunciationConfig.applyTo(recognizer)
            }
            await new Promise((resolve, reject) => {
              try {
                recognizer.recognizeOnceAsync(async (result) => {
                  const transcript = result.text
                  const resultJson = JSON.parse(result.json)
                  let errorTypes
                  let pronunciationResult
                  if (!isFreeResponse) {
                    if (resultJson.RecognitionStatus == RecognitionStatus.InitialSilenceTimeout) {
                      // do ntg
                    } else {
                      errorTypes = resultJson.NBest[0].Words.map(
                        (word) => word.PronunciationAssessment.ErrorType
                      )
                      pronunciationResult = PronunciationAssessmentResult.fromResult(result)
                    }
                  }
                  console.log(`Recognized: ${transcript}`)
                  console.log('Error Types: ', errorTypes ?? [])
                  console.log('Score: ', pronunciationResult?.accuracyScore ?? 0)

                  tests.push({
                    raw_result: result,
                    file_id: recording.id,
                    category: referenceText,
                    // ...(pinyinResult && {
                    //   pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
                    // }),
                    error_types: errorTypes ?? [],
                    hypothesis: result.text,
                    hypothesis_score: pronunciationResult?.accuracyScore ?? 0,
                    // remaining_text: toCompare,
                    // extra_text: extraWords,
                    language: validationData.language,
                    user_id: user.id,
                    chapter_id: validationData.chapter_id,
                    story_id: validationData.story_id,
                    session_id: validationData.session_id,
                    device: validationData.device,
                    calibrations: validationData.calibration,
                    volumes: validationData.actual_volume,
                    minimum_volume: validationData.minimum_volume,
                  })
                  recordingResults.push({
                    raw_result: result,
                    file_id: recording.id,
                    category: referenceText,
                    // ...(pinyinResult && {
                    //   pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
                    // }),
                    // error_types: errorTypes,
                    hypothesis: result.text,
                    hypothesis_score: pronunciationResult?.accuracyScore ?? 0,
                    // remaining_text: toCompare,
                    // extra_text: extraWords,
                    language: validationData.language,
                    user_id: user.id,
                    chapter_id: validationData.chapter_id,
                    story_id: validationData.story_id,
                    session_id: validationData.session_id,
                    device: validationData.device,
                    calibrations: validationData.calibration,
                    volumes: validationData.actual_volume,
                    minimum_volume: validationData.minimum_volume,
                    isRepeat: validationData.is_repeat ?? false,
                  })

                  recognizer.close()
                  resolve(tests)
                })
              } catch (error) {
                console.log(error)
                reject({ score: 0, remaining: [] })
              }
            })
          }
        }
      }

      const highestScore = Math.max.apply(
        Math,
        recordingResults.map(function (result) {
          return result.hypothesis_score
        })
      )
      // WIP: Find duplicate hypothesis_score
      // const sameScore = [...new Set(recordingResults.filter((e, i, array) => array.indexOf(e.hypothesis_score) !== i ))]

      const result = recordingResults.findIndex(
        (highestScoreIndex) => highestScoreIndex.hypothesis_score >= highestScore
      )

      const recordingResult = await RecordingResult.create(recordingResults[result])

      const story = await Story.findOrFail(validationData.story_id)
      let latestSession: Session | null
      if (story.type === StoryType.GAME) {
        await Database.transaction(async (trx) => {
          const chapter = await Chapter.findOrFail(validationData.chapter_id)
          latestSession = await Session.query()
            .where('user_id', auth.user?.id ?? 0)
            .whereNull('ended_at')
            .orderBy('id', 'desc')
            .firstOrFail()

          let option = chapter.options as Option[]
          const correctOption = option.findIndex(
            (option) =>
              option.answer &&
              option.value === recordingResult.category &&
              recordingResult.hypothesisScore >= (option?.threshold ?? 0)
          )

          if (correctOption !== -1) {
            latestSession.gameScore += 1
          }

          latestSession.fullGameScore += 1

          latestSession.useTransaction(trx)
          await latestSession.save()
        })
      }

      // format it to match with soapbox
      return response.status(200).send({
        success: true,
        data: {
          results: tests,
          final_answer: tests[result],
          language_code: validationData.language,
          time: DateTime.now().toISO(),
          recording_id: recording.id,
        },
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  // chinese azure + english azure
  // public async store({ request, response, auth }: HttpContextContract) {
  //   const user = await auth.authenticate()

  //   try {
  //     const validationSchema = schema.create({
  //       file: schema.file({
  //         size: '200mb',
  //         extnames: ['wav'],
  //       }),
  //       category: schema.array().members(schema.string.optional()),
  //       device: schema.string.optional(),
  //       user_token: schema.string.optional(),
  //       language: schema.string(),
  //       chapter_id: schema.number.optional(),
  //       story_id: schema.number.optional(),
  //       session_id: schema.number.optional(),
  //       calibration: schema.array().members(schema.string()),
  //       minimum_volume: schema.string(),
  //       actual_volume: schema.array().members(schema.string()),
  //       is_repeat: schema.boolean.optional(),
  //     })

  //     const validationData = await request.validate({
  //       schema: validationSchema,
  //     })

  //     // standardize language format
  //     validationData.language = validationData.language.replace('_', '-')

  //     const uploadBucket = await uploadToS3Bucket(
  //       validationData.file,
  //       process.env.S3_bucket ?? 'hummusedu',
  //       user.email
  //     )

  //     // upload to s3 for backup
  //     const recording = await File.create({
  //       src: uploadBucket.url,
  //       type: 'recording',
  //       userId: user.id,
  //     })

  //     let azureAuth = await AzureAuth.query().where('status', '=', 1).first()
  //     if (azureAuth === null) {
  //       const headers = {
  //         headers: {
  //           'Ocp-Apim-Subscription-Key': '0e8cacdd8b6f482eac6f698f70f7b8e5',
  //           'Content-Type': 'application/x-www-form-urlencoded',
  //         },
  //       }

  //       const tokenResponse = await axios.post(
  //         `https://southeastasia.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
  //         null,
  //         headers
  //       )

  //       azureAuth = await AzureAuth.create({
  //         token: tokenResponse.data,
  //         status: 0,
  //       })
  //     }

  //     const speechConfig = SpeechConfig.fromAuthorizationToken(
  //       azureAuth.token,
  //       process.env.AZURE_REGION ?? 'southeastasia'
  //     )
  //     speechConfig.outputFormat = 1
  //     speechConfig.speechRecognitionLanguage = validationData.language

  //     const fileName = `${uuid()}.${validationData.file.extname}`
  //     const soxFile = `/tmp/${fileName}`

  //     // for ios app
  //     // post process file buffer to fix fmt missing
  //     try {
  //       const { stdout, stderr } = await awaitExec(`sox ${validationData.file.tmpPath} ${soxFile}`)

  //       if (stderr) {
  //         console.log(`stderr: ${stderr}`)
  //       }

  //       console.log(`stdout: ${stdout}`)
  //     } catch (error) {
  //       console.log(`error: ${error.message}`)
  //     }

  //     console.log('Language: ', validationData.language)
  //     const audioConfig = AudioConfig.fromWavFileInput(fs.readFileSync(`${soxFile}`))
  //     const recordingResults: any[] = []
  //     const tests: any[] = []
  //     const uniqueCategories = uniq(validationData.category)
  //     for (const referenceText of uniqueCategories) {
  //       console.log('Reference: ', referenceText)
  //       if (referenceText) {
  //         const isFreeResponse = referenceText == 'free response'
  //         const recognizer = new SpeechRecognizer(speechConfig, audioConfig)
  //         let pronunciationConfig
  //         if (!isFreeResponse) {
  //           pronunciationConfig = new PronunciationAssessmentConfig(
  //             referenceText,
  //             PronunciationAssessmentGradingSystem.HundredMark
  //           )
  //           pronunciationConfig.applyTo(recognizer)
  //         }
  //         await new Promise((resolve, reject) => {
  //           try {
  //             recognizer.recognizeOnceAsync(async (result) => {
  //               const transcript = result.text
  //               console.log(`Recognized: ${transcript}`)

  //               const resultJson = JSON.parse(result.json)
  //               // console.log(resultJson)
  //               let errorTypes
  //               let pronunciationResult
  //               if (!isFreeResponse) {
  //                 errorTypes = resultJson.NBest[0].Words.map(
  //                   (word) => word.PronunciationAssessment.ErrorType
  //                 )
  //                 console.log('Error Types: ', errorTypes)

  //                 pronunciationResult = PronunciationAssessmentResult.fromResult(result)
  //                 console.log('Score: ', pronunciationResult.accuracyScore)
  //               }

  //               tests.push({
  //                 raw_result: result,
  //                 file_id: recording.id,
  //                 category: referenceText,
  //                 // ...(pinyinResult && {
  //                 //   pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
  //                 // }),
  //                 error_types: errorTypes ?? [],
  //                 hypothesis: result.text,
  //                 hypothesis_score: pronunciationResult?.accuracyScore ?? 0,
  //                 // remaining_text: toCompare,
  //                 // extra_text: extraWords,
  //                 language: validationData.language,
  //                 user_id: user.id,
  //                 chapter_id: validationData.chapter_id,
  //                 story_id: validationData.story_id,
  //                 session_id: validationData.session_id,
  //                 device: validationData.device,
  //                 calibrations: validationData.calibration,
  //                 volumes: validationData.actual_volume,
  //                 minimum_volume: validationData.minimum_volume,
  //               })
  //               recordingResults.push({
  //                 raw_result: result,
  //                 file_id: recording.id,
  //                 category: referenceText,
  //                 // ...(pinyinResult && {
  //                 //   pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
  //                 // }),
  //                 // error_types: errorTypes,
  //                 hypothesis: result.text,
  //                 hypothesis_score: pronunciationResult?.accuracyScore ?? 0,
  //                 // remaining_text: toCompare,
  //                 // extra_text: extraWords,
  //                 language: validationData.language,
  //                 user_id: user.id,
  //                 chapter_id: validationData.chapter_id,
  //                 story_id: validationData.story_id,
  //                 session_id: validationData.session_id,
  //                 device: validationData.device,
  //                 calibrations: validationData.calibration,
  //                 volumes: validationData.actual_volume,
  //                 minimum_volume: validationData.minimum_volume,
  //                 isRepeat: validationData.is_repeat ?? false,
  //               })

  //               recognizer.close()
  //               resolve(true)
  //             })
  //           } catch (error) {
  //             console.log(error)
  //             reject({ score: 0, remaining: [] })
  //           }
  //         })
  //       }
  //     }

  //     const highestScore = Math.max.apply(
  //       Math,
  //       recordingResults.map(function (result) {
  //         return result.hypothesis_score
  //       })
  //     )
  //     // WIP: Find duplicate hypothesis_score
  //     // const sameScore = [...new Set(recordingResults.filter((e, i, array) => array.indexOf(e.hypothesis_score) !== i ))]

  //     const result = recordingResults.findIndex(
  //       (highestScoreIndex) => highestScoreIndex.hypothesis_score >= highestScore
  //     )
  //     await RecordingResult.create(recordingResults[result])

  //     // format it to match with soapbox
  //     return response.status(200).send({
  //       success: true,
  //       data: {
  //         results: tests,
  //         language_code: validationData.language,
  //         time: DateTime.now().toISO(),
  //         recording_id: recording.id,
  //       },
  //     })
  //   } catch (error) {
  //     console.log(error)
  //     return response.status(400).send(error)
  //   }
  // }

  public async instantStore({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        file: schema.file({
          size: '200mb',
          extnames: ['wav'],
        }),
        category: schema.array().members(schema.string.optional()),
        device: schema.string.optional(),
        user_token: schema.string.optional(),
        language: schema.string(),
        chapter_id: schema.number.optional(),
        story_id: schema.number.optional(),
        session_id: schema.number.optional(),
        calibration: schema.array.optional().members(schema.string()),
        minimum_volume: schema.string(),
        actual_volume: schema.array.optional().members(schema.string()),
        is_repeat: schema.boolean.optional(),
      })

      const validationData = await request.validate({
        schema: validationSchema,
      })

      const uploadBucket = await uploadToS3Bucket(
        validationData.file,
        process.env.S3_bucket ?? 'hummusedu',
        'instant-app'
      )

      // upload to s3 for backup
      const recording = await File.create({
        src: uploadBucket.url,
        type: 'recording',
        // instant app no user id
      })

      let azureAuth = await AzureAuth.query().where('status', '=', 1).first()
      if (azureAuth === null) {
        const headers = {
          headers: {
            'Ocp-Apim-Subscription-Key': '0e8cacdd8b6f482eac6f698f70f7b8e5',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }

        const tokenResponse = await axios.post(
          `https://southeastasia.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
          null,
          headers
        )

        azureAuth = await AzureAuth.create({
          token: tokenResponse.data,
          status: 0,
        })
      }

      const speechConfig = SpeechConfig.fromAuthorizationToken(
        azureAuth.token,
        process.env.AZURE_REGION ?? 'southeastasia'
      )
      speechConfig.outputFormat = 1
      speechConfig.speechRecognitionLanguage = validationData.language

      const fileName = `${uuid()}.${validationData.file.extname}`
      const soxFile = `/tmp/${fileName}`

      // for ios app
      // post process file buffer to fix fmt missing
      try {
        const { stdout, stderr } = await awaitExec(`sox ${validationData.file.tmpPath} ${soxFile}`)

        if (stderr) {
          console.log(`stderr: ${stderr}`)
        }

        console.log(`stdout: ${stdout}`)
      } catch (error) {
        console.log(`error: ${error.message}`)
      }

      console.log('Language: ', validationData.language)
      const audioConfig = AudioConfig.fromWavFileInput(fs.readFileSync(`${soxFile}`))
      const recordingResults: any[] = []
      const tests: any[] = []
      const uniqueCategories = uniq(validationData.category)
      for (const referenceText of uniqueCategories) {
        if (referenceText) {
          console.log('reference: ', referenceText)
          const recognizer = new SpeechRecognizer(speechConfig, audioConfig)
          const pronunciationConfig = new PronunciationAssessmentConfig(
            referenceText,
            PronunciationAssessmentGradingSystem.HundredMark
          )
          pronunciationConfig.applyTo(recognizer)
          await new Promise((resolve, reject) => {
            try {
              recognizer.recognizeOnceAsync(async (result) => {
                const transcript = result.text
                const resultJson = JSON.parse(result.json)
                const errorTypes = resultJson.NBest[0].Words.map(
                  (word) => word.PronunciationAssessment.ErrorType
                )
                console.log(`Recognized: ${transcript}`)
                console.log('Error Types: ', errorTypes)
                const pronunciationResult = PronunciationAssessmentResult.fromResult(result)
                console.log('Score: ', pronunciationResult.accuracyScore)

                tests.push({
                  raw_result: result,
                  file_id: recording.id,
                  category: referenceText,
                  // ...(pinyinResult && {
                  //   pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
                  // }),
                  error_types: errorTypes,
                  hypothesis: result.text,
                  hypothesis_score: pronunciationResult.accuracyScore,
                  // remaining_text: toCompare,
                  // extra_text: extraWords,
                  language: validationData.language,
                  // user_id: user.id,
                  chapter_id: validationData.chapter_id,
                  story_id: validationData.story_id,
                  session_id: validationData.session_id,
                  device: validationData.device,
                  calibrations: validationData.calibration,
                  volumes: validationData.actual_volume,
                  minimum_volume: validationData.minimum_volume,
                })
                recordingResults.push({
                  raw_result: result,
                  file_id: recording.id,
                  category: referenceText,
                  // ...(pinyinResult && {
                  //   pinyin_hypothesis: pinyinResult.slice(0, -1).flat().join(' '),
                  // }),
                  // error_types: errorTypes,
                  hypothesis: result.text,
                  hypothesis_score: pronunciationResult.accuracyScore,
                  // remaining_text: toCompare,
                  // extra_text: extraWords,
                  language: validationData.language,
                  // user_id: user.id,
                  chapter_id: validationData.chapter_id,
                  story_id: validationData.story_id,
                  session_id: validationData.session_id,
                  device: validationData.device,
                  calibrations: validationData.calibration,
                  volumes: validationData.actual_volume,
                  minimum_volume: validationData.minimum_volume,
                  isRepeat: validationData.is_repeat ?? false,
                })

                recognizer.close()
                resolve(true)
              })
            } catch (error) {
              console.log(error)
              reject({ score: 0, remaining: [] })
            }
          })
        }
      }

      const highestScore = Math.max.apply(
        Math,
        recordingResults.map(function (result) {
          return result.hypothesis_score
        })
      )
      // WIP: Find duplicate hypothesis_score
      // const sameScore = [...new Set(recordingResults.filter((e, i, array) => array.indexOf(e.hypothesis_score) !== i ))]

      const result = recordingResults.findIndex(
        (highestScoreIndex) => highestScoreIndex.hypothesis_score >= highestScore
      )
      await RecordingResult.create(recordingResults[result])

      // format it to match with soapbox
      return response.status(200).send({
        success: true,
        data: {
          results: tests,
          language_code: validationData.language,
          time: DateTime.now().toISO(),
          recording_id: recording.id,
        },
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  public async find({ auth, request, response }: HttpContextContract) {
    const user = await auth.authenticate()

    // TODO: use validation
    try {
      const validationSchema = schema.create({
        status_uri: schema.string(),
        recording_id: schema.number(),
        chapter_id: schema.number.optional(),
        story_id: schema.number.optional(),
        session_id: schema.number.optional(),
        calibrations: schema.array().members(schema.string()),
        minimum_volume: schema.string(),
        volumes: schema.array().members(schema.string()),
        device: schema.string.optional(),
      })

      const validationData = await request.validate({
        schema: validationSchema,
      })

      // const chapterId = request.input('chapter')
      // const activityId = request.input('activity')
      // const recordingId = request.input('recording')
      // const device = request.input('device')
      // const link = request.input('url')
      const res = await axios.get(validationData.status_uri)

      const highestScore = Math.max.apply(
        Math,
        res.data.results.map(function (result) {
          return result.hypothesis_score
        })
      )

      const highestScoreIndex = res.data.results.findIndex(
        (highestScoreIndex) => highestScoreIndex.hypothesis_score >= highestScore
      )

      // TODO: figure out how to get category
      if (highestScoreIndex >= 0) {
        await RecordingResult.create({
          rawResult: res.data,
          fileId: validationData.recording_id,
          category: res.data.results[highestScoreIndex].category,
          hypothesis: res.data.results[highestScoreIndex].category,
          hypothesisScore: res.data.results[highestScoreIndex].hypothesis_score,
          language: res.data.language_code,
          userId: user.id,
          chapterId: validationData.chapter_id,
          device: validationData.device,
          calibrations: validationData.calibrations,
          volumes: validationData.volumes,
          minimumVolume: validationData.minimum_volume,
          sessionId: validationData.session_id,
        })
      } else {
        // result not found
        await RecordingResult.create({
          rawResult: res.data,
          fileId: validationData.recording_id,
          // category: validationData.category?.join(' '),
          language: res.data.language_code,
          userId: user.id,
          chapterId: validationData.chapter_id,
          device: validationData.device,
          calibrations: validationData.calibrations,
          volumes: validationData.volumes,
          minimumVolume: validationData.minimum_volume,
          sessionId: validationData.session_id,
        })
      }

      return response.status(200).send({
        data: res.data,
      })
    } catch (error) {
      console.log('find', error)
      if (error.response?.status === 404) {
        return response.status(400).send({
          code: 'request.not.ready',
        })
      }

      return response.status(400).send(error)
    }
  }

  public async instantFind({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        status_uri: schema.string(),
        recording_id: schema.number(),
        chapter_id: schema.number.optional(),
        story_id: schema.number.optional(),
        session_id: schema.number.optional(),
        calibrations: schema.array.optional().members(schema.string()),
        minimum_volume: schema.string(),
        volumes: schema.array.optional().members(schema.string()),
        device: schema.string.optional(),
      })

      const validationData = await request.validate({
        schema: validationSchema,
      })

      const res = await axios.get(validationData.status_uri)

      const highestScore = Math.max.apply(
        Math,
        res.data.results.map(function (result) {
          return result.hypothesis_score
        })
      )

      const highestScoreIndex = res.data.results.findIndex(
        (highestScoreIndex) => highestScoreIndex.hypothesis_score >= highestScore
      )

      // TODO: figure out how to get category
      if (highestScoreIndex >= 0) {
        await RecordingResult.create({
          rawResult: res.data,
          fileId: validationData.recording_id,
          category: res.data.results[highestScoreIndex].category,
          hypothesis: res.data.results[highestScoreIndex].category,
          hypothesisScore: res.data.results[highestScoreIndex].hypothesis_score,
          language: res.data.language_code,
          chapterId: validationData.chapter_id,
          device: validationData.device,
          calibrations: validationData.calibrations,
          volumes: validationData.volumes,
          minimumVolume: validationData.minimum_volume,
          // sessionId: validationData.session_id,
        })
      } else {
        // result not found
        await RecordingResult.create({
          rawResult: res.data,
          fileId: validationData.recording_id,
          // category: validationData.category?.join(' '),
          language: res.data.language_code,
          chapterId: validationData.chapter_id,
          device: validationData.device,
          calibrations: validationData.calibrations,
          volumes: validationData.volumes,
          minimumVolume: validationData.minimum_volume,
          // sessionId: validationData.session_id,
        })
      }

      return response.status(200).send({
        data: res.data,
      })
    } catch (error) {
      console.log('find', error)
      if (error.response?.status === 404) {
        return response.status(400).send({
          code: 'request.not.ready',
        })
      }

      return response.status(400).send(error)
    }
  }

  public async azureToken({ response }: HttpContextContract) {
    const headers = {
      headers: {
        'Ocp-Apim-Subscription-Key': '0e8cacdd8b6f482eac6f698f70f7b8e5',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    }

    try {
      const tokenResponse = await axios.post(
        `https://southeastasia.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
        null,
        headers
      )

      return response.status(200).send({
        success: true,
        data: tokenResponse.data,
        region: 'southeastasia',
      })
    } catch (err) {
      return response.status(400).send(err)
    }
  }
}
