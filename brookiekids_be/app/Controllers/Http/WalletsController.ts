import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Transaction from 'App/Models/Transaction'
import Wallet from 'App/Models/Wallet'
import _ from 'lodash'

export default class WalletsController {
  public async findMyWallet({ response, auth }: HttpContextContract) {
    try {
      const user = await auth.authenticate()
      const myWallet = await Wallet.query()
        .where('user_id', user.id)
        .withCount('transactions', (query) => {
          query.sum('amount').as('balance').where('status', 'confirmed')
        })
        .firstOrFail()

      return response.status(200).send(myWallet)
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  public async findMyTransactions({ request, response, auth }: HttpContextContract) {
    try {
      const user = await auth.authenticate()
      const wallet = await Wallet.findByOrFail('user_id', user.id)

      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const sort = request.input('sort', 'id:desc').split(':')
      //   const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const transactions = await Transaction.query()
        .where('user_id', user.id)
        .where('wallet_id', wallet.id)
        .where('status', 'confirmed')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(transactions)
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  public async findTransactions({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const transactions = await Transaction.filter(filters)
        .preload('user')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(transactions)
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  public async freeCredits({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        user_id: schema.number([rules.exists({ column: 'id', table: 'users' })]),
        amount: schema.number(),
      })
      const validationData = await request.validate({ schema: validationSchema })
      const wallet = await Wallet.findByOrFail('user_id', validationData.user_id)

      await Database.transaction(async (trx) => {
        const sendFreeCredit = new Transaction()
        sendFreeCredit.walletId = wallet.id
        sendFreeCredit.userId = wallet.userId
        sendFreeCredit.title = 'Top-up'
        sendFreeCredit.description = 'Free Credit'
        sendFreeCredit.type = 'deposit'
        sendFreeCredit.status = 'confirmed'
        sendFreeCredit.amount = validationData.amount
        sendFreeCredit.amountIn = validationData.amount
        sendFreeCredit.amountOut = 0
        sendFreeCredit.remark = 'Free credit'

        sendFreeCredit.useTransaction(trx)
        await sendFreeCredit.save()
      })

      return response.ok({ success: true })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
