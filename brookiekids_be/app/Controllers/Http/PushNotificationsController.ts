import Event from '@ioc:Adonis/Core/Event'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import PushNotification from 'App/Models/PushNotification'
import User from 'App/Models/User'
import { uploadToS3Bucket } from './FilesController'
import { parse } from 'csv-parse'
import fs from 'fs'

export default class PushNotificationsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const notifications = await PushNotification.query()
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(notifications)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const notification = await PushNotification.query().where('id', params.id).first()

    return response.send({
      data: notification,
    })
  }

  // admin to send app notification
  public async sendAppNotification({ response, request, auth }: HttpContextContract) {
    try {
      console.log('sendAppNotification', request.all())
      const user = await User.findOrFail(auth.user?.id)
      const validationSchema = schema.create({
        title: schema.string(),
        body: schema.string.optional(),
        recipients: schema.file({
          size: '200mb',
          extnames: ['csv'],
        }),
        // recipients: schema
        //   .array()
        //   .members(schema.number([rules.exists({ column: 'id', table: 'users' })])),
        image: schema.file.optional(),
      })

      const validationData = await request.validate({
        schema: validationSchema,
      })

      const emails: string[] = []
      const recipients: number[] = []

      const parser = parse({
        delimiter: ',',
        columns: true,
        trim: true,
        skipEmptyLines: true,
        cast: true,
        bom: true,
      })

      if (fs.existsSync(validationData.recipients.tmpPath ?? '')) {
        const fsStream = fs.createReadStream(validationData.recipients.tmpPath!)
        await new Promise((resolve) => {
          fsStream
            .pipe(parser)
            .on('data', (data) => {
              if (data.email) {
                emails.push(data.email)
              } else {
                throw new Error('wrong csv format')
              }
            })
            .on('end', async () => {
              if (emails.length <= 0) {
                throw new Error('wrong csv format')
              }
              const users = await User.query()
                .whereIn('email', emails)
                .whereHas('devices', (query) => query.whereNotNull('token'))
              for (const user of users) {
                recipients.push(user.id)
              }
              console.log(recipients)
              resolve(true)
            })
        })
      }

      let src: string | null = null
      if (validationData.image) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.image,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        // console.log(uploadBucket)
        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload',
          })
        }

        src = uploadBucket.url
      }

      // Send a message to the device corresponding to the provided
      // registration token.
      const newMsg = new PushNotification()
      newMsg.title = validationData.title
      newMsg.body = validationData?.body ?? ''
      newMsg.imageUrl = src ?? ''
      newMsg.recipients = recipients
      // let recipients: number[] = []
      // if (validationData?.recipients && validationData?.recipients?.length > 0) {
      //   newMsg.recipients = JSON.stringify(validationData.recipients)
      //   recipients = validationData.recipients
      // } else {
      //   const users = await User.query()
      //   recipients = users.map((item) => item.id)
      // }
      await newMsg.save()

      console.log({
        user_ids: recipients,
        title: validationData.title,
        body: validationData.body,
        image_url: src,
        id: newMsg.id,
      })

      Event.emit('send:messages', {
        user_ids: recipients,
        title: validationData.title,
        body: validationData.body,
        image_url: src,
        id: newMsg.id,
      })

      return response.send({ success: true, data: newMsg })
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }
}
