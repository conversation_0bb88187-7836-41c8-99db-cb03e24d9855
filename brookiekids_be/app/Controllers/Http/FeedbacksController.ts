// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import { schema, rules } from '@ioc:Adonis/Core/Validator'
// import Database from '@ioc:Adonis/Lucid/Database'
// import Event from '@ioc:Adonis/Core/Event'
// import Feedback from 'App/Models/Feedback'
// import FeedbackHistory from 'App/Models/FeedbackHistory'

// export default class FeedbacksController {
//   public async find({ response, request }: HttpContextContract) {
//     const page = request.input('page', 1)
//     const limit = request.input('limit', 20)
//     const sort = request.input('sort', 'id:desc').split(':')

//     const feedbacks = await Feedback.filter(request.all())
//       .preload('user')
//       .orderBy(sort[0], sort[1])
//       .paginate(page, limit)

//     return response.status(200).send(feedbacks)
//   }

//   public async findMyFeedbacks({ auth, response, request }: HttpContextContract) {
//     const user = await auth.authenticate()
//     const page = request.input('page', 1)
//     const limit = request.input('limit', 20)
//     const sort = request.input('sort', 'id:desc').split(':')

//     const feedbacks = await Feedback.filter(request.all())
//       .where('user_id', user.id)
//       .preload('user')
//       .orderBy(sort[0], sort[1])
//       .paginate(page, limit)

//     return response.status(200).send(feedbacks)
//   }

//   public async findOne({ response, params }: HttpContextContract) {
//     const feedback = await Feedback.query()
//       .where('id', params.id)
//       .preload('user')
//       .preload('histories')
//       .first()

//     return response.send({
//       data: feedback,
//     })
//   }

//   public async findHistories({ response, request, params: { id } }: HttpContextContract) {
//     const page = request.input('page', 1)
//     const limit = request.input('limit', 20)
//     const sort = request.input('sort', 'created_at:desc').split(':')
//     const histories = await FeedbackHistory.query()
//       .where('feedback_id', id)
//       .preload('feedback')
//       .orderBy(sort[0], sort[1])
//       .paginate(page, limit)

//     return response.status(200).send(histories)
//   }

//   public async create({ auth, request, response }: HttpContextContract) {
//     const user = await auth.authenticate()

//     const validationSchema = schema.create({
//       subject: schema.string.optional({ trim: true }),
//       message: schema.string({ trim: true }),
//     })

//     const validationData = await request.validate({
//       schema: validationSchema,
//     })

//     const createFeedback = await Database.transaction(async (trx) => {
//       const feedback = new Feedback()
//       feedback.merge({
//         ...validationData,
//         userId: user.id,
//         lastMessage: validationData.message,
//       })
//       await feedback.useTransaction(trx).save()

//       const feedbackHistory = new FeedbackHistory()
//       feedbackHistory.merge({
//         feedbackId: feedback.id,
//         fromUserId: user.id,
//         toUserId: 1, // hard code 1 as admin
//         content: validationData.message,
//       })
//       await feedbackHistory.useTransaction(trx).save()

//       return feedback
//     })

//     return response.status(200).send({
//       success: true,
//       data: createFeedback,
//     })
//   }

//   public async createHistory({ auth, request, response, params: { id } }: HttpContextContract) {
//     const user = await auth.authenticate()

//     const validationSchema = schema.create({
//       content: schema.string(),
//       to_user_id: schema.number([rules.exists({ table: 'users', column: 'id' })]),
//     })

//     const validationData = await request.validate({
//       schema: validationSchema,
//     })

//     const feedback = await Feedback.query().where('id', id).first()

//     if (!feedback) {
//       return response.notFound({ message: 'feedback not found' })
//     }

//     try {
//       const history = await Database.transaction(async (trx) => {
//         const feedbackHistory = new FeedbackHistory()
//         feedbackHistory.merge({
//           feedbackId: id,
//           fromUserId: user.id,
//           toUserId: validationData.to_user_id,
//           content: validationData.content,
//         })
//         await feedbackHistory.useTransaction(trx).save()

//         feedback.merge({
//           updatedAt: feedbackHistory.createdAt,
//           lastMessage: feedbackHistory.content,
//         })
//         await feedback.useTransaction(trx).save()

//         return feedbackHistory
//       })

//       // admin replied
//       if (user.id === 1) {
//         Event.emit('notify:user', {
//           user_ids: [history.toUserId],
//           title: `BrookieKids sent you a message`,
//           body: history.content,
//           type: 'dialogs',
//           key: history.id,
//           scope: 0,
//         })
//       }

//       return response.status(200).send({ data: history, success: true })
//     } catch (e) {
//       console.log(e)
//       return response.badRequest(e)
//     }
//   }

//   public async update({ request, response, params }: HttpContextContract) {
//     const validationSchema = schema.create({
//       subject: schema.string.optional({ trim: true }),
//       message: schema.string.optional({ trim: true }),
//     })

//     const validationData = await request.validate({
//       schema: validationSchema,
//     })

//     const updateFeedback = await Feedback.query().where('id', params.id).update(validationData)

//     return response.status(200).send({
//       success: true,
//       data: updateFeedback,
//     })
//   }

//   public async delete({ response, params }: HttpContextContract) {
//     const deleteFeedback = await Feedback.query().where('id', params.id).delete()

//     return response.status(200).send({
//       success: true,
//       data: deleteFeedback,
//     })
//   }
// }
