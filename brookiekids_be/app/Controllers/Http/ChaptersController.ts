import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Chapter from 'App/Models/Chapter'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Story from 'App/Models/Story'
import { createMediaConvertJob, uploadToS3Bucket } from './FilesController'
import User from 'App/Models/User'
import Database from '@ioc:Adonis/Lucid/Database'
import File from 'App/Models/File'
import _ from 'lodash'

export default class ChaptersController {
  public async findAll({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const allChapters = await Chapter.filter(filters)
      .preload('video')
      .preload('story')
      .preload('fallbackChapter')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(allChapters)
  }

  public async findChaptersByPackAndLevel({ params, request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const allChapters = await Chapter.filter(filters)
      .whereHas('story', (query) => {
        query.where('pack_id', params.pack_id).where('level', params.level)
      })
      .preload('story', (query) => query.preload('chapters'))
      .preload('fallbackChapter')
      .preload('video')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(allChapters)
  }

  public async findChaptersByStoryId({ params, request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const chapters = await Chapter.filter(filters)
      .where('story_id', params.story_id)
      .preload('story')
      .preload('video')
      .preload('fallbackChapter')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(chapters)
  }

  public async create({ request, response, auth }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)

    const validationSchema = schema.create({
      title: schema.string(),
      description: schema.string.optional(),
      story_id: schema.number(),
      options: schema.array.optional().members(
        schema.object().members({
          value: schema.string(),
          chapterId: schema.number.optional([
            rules.exists({
              table: 'chapters',
              column: 'id',
            }),
          ]),
          answer: schema.boolean.optional(),
          threshold: schema.number(),
        })
      ),
      video_file: schema.file.optional(
        {
          size: '400mb',
          extnames: ['mp4'],
        },
        [rules.requiredIfNotExists('video_url')]
      ),
      fallback_chapter_id: schema.number.optional([
        rules.exists({ table: 'chapters', column: 'id' }),
      ]),
      is_free_response: schema.boolean(),
      audio_file: schema.file.optional({
        extnames: ['mp3'],
        size: '10mb',
      }),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    let videoSrc = ''
    if (validationData.video_file) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.video_file,
        process.env.S3_BUCKET_VIDEO ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }
      const inputS3Url = `s3://${process.env.S3_BUCKET_VIDEO}/${uploadBucket.key}`
      const outputS3Url = `s3://${process.env.S3_BUCKET_HLS}/`
      // console.log('input: ', inputS3Url)
      // console.log('output: ', outputS3Url)
      const job = await createMediaConvertJob(inputS3Url, outputS3Url)
      if (job.Status !== 'SUBMITTED') {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      videoSrc = `${process.env.S3_CLOUDFRONT_VIDEO}${uploadBucket.key.replace(
        '.mp4',
        '_1080p.m3u8'
      )}`
    }

    let audioSrc: string
    if (validationData.audio_file) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.audio_file,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      audioSrc = uploadBucket.url
    }

    const findStory = await Story.findOrFail(validationData.story_id)
    const result = await Database.transaction(async (trx) => {
      try {
        let fileId
        if (videoSrc) {
          const chapterVideo = new File()
          chapterVideo.src = videoSrc
          chapterVideo.type = 'video'
          chapterVideo.useTransaction(trx)
          await chapterVideo.save()
          fileId = chapterVideo.id
        }

        const newChapter = new Chapter()
        newChapter.title = validationData.title
        if (validationData.description) {
          newChapter.description = validationData.description
        }
        newChapter.storyId = validationData.story_id
        if (fileId) {
          newChapter.fileId = fileId
        }
        if (validationData.options) {
          newChapter.options = validationData.options
        }
        if (validationData.fallback_chapter_id) {
          newChapter.fallbackChapterId = validationData.fallback_chapter_id
        }
        newChapter.isFreeResponse = validationData.is_free_response
        if (validationData.is_free_response) {
          newChapter.options = [{ value: 'free response' }]
        } else {
          newChapter.options = validationData.options ? validationData.options : null
        }
        if (audioSrc) {
          newChapter.audioUrl = audioSrc
        }
        newChapter.useTransaction(trx)
        await newChapter.save()

        if (findStory.defaultChapterId === null) {
          findStory.defaultChapterId = newChapter.id
          findStory.useTransaction(trx)
          await findStory.save()
        }

        return newChapter
      } catch (error) {
        console.log(error)
      }
    })

    return response.status(200).send({ success: true, data: result })
  }

  public async update({ request, response, params, auth }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)
    const validationSchema = schema.create({
      title: schema.string.optional(),
      description: schema.string.optional(),
      story_id: schema.number.optional([rules.exists({ table: 'stories', column: 'id' })]),
      options: schema.array.optional().members(
        schema.object().members({
          value: schema.string(),
          chapterId: schema.number.optional([
            rules.exists({
              table: 'chapters',
              column: 'id',
            }),
          ]),
          threshold: schema.number(),
          answer: schema.boolean.optional(),
        })
      ),
      video_file: schema.file.optional({
        size: '400mb',
        extnames: ['mp4'],
      }),
      // video_url: schema.string.optional(),
      // video_id: schema.number([rules.exists({ table: 'videos', column: 'id' })]),
      // audio_url: schema.string.optional(),
      // audio_file_name: schema.string.optional(),
      fallback_chapter_id: schema.number.optional([
        rules.exists({ table: 'chapters', column: 'id' }),
      ]),
      is_free_response: schema.boolean(),
      is_video_removed: schema.boolean.optional(),
      audio_file: schema.file.optional({
        extnames: ['mp3'],
        size: '10mb',
      }),
      is_audio_removed: schema.boolean.optional(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const updateChapter = await Chapter.findOrFail(params.id)
    let videoSrc = ''
    if (validationData.video_file) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.video_file,
        process.env.S3_BUCKET_VIDEO ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }
      const inputS3Url = `s3://${process.env.S3_BUCKET_VIDEO}/${uploadBucket.key}`
      const outputS3Url = `s3://${process.env.S3_BUCKET_HLS}/`
      // console.log('input: ', inputS3Url)
      // console.log('output: ', outputS3Url)
      const job = await createMediaConvertJob(inputS3Url, outputS3Url)
      if (job.Status !== 'SUBMITTED') {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      videoSrc = `${process.env.S3_CLOUDFRONT_VIDEO}${uploadBucket.key.replace(
        '.mp4',
        '_1080p.m3u8'
      )}`
    }

    let audioSrc: string
    if (validationData.audio_file) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.audio_file,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      audioSrc = uploadBucket.url
    }

    // if exist, use same entry to update
    const videoFile = await File.find(updateChapter.fileId ?? 0)

    const result = await Database.transaction(async (trx) => {
      try {
        let fileId
        if (videoSrc || validationData.is_video_removed) {
          if (videoFile) {
            videoFile.src = validationData.is_video_removed ? null : videoSrc
            videoFile.useTransaction(trx)
            await videoFile.save()
          } else {
            const chapterVideo = new File()
            chapterVideo.src = videoSrc ? videoSrc : null
            chapterVideo.type = 'video'
            chapterVideo.userId = user.id
            chapterVideo.useTransaction(trx)
            await chapterVideo.save()
            fileId = chapterVideo.id
          }
        }

        if (validationData.title) {
          updateChapter.title = validationData.title
        }
        if (validationData.description) {
          updateChapter.description = validationData.description
        }
        if (validationData.story_id) {
          updateChapter.storyId = validationData.story_id
        }
        if (fileId) {
          updateChapter.fileId = fileId
        }
        if (validationData.options) {
          updateChapter.options = validationData.options
        } else {
          updateChapter.options = null
        }
        if (validationData.fallback_chapter_id) {
          updateChapter.fallbackChapterId = validationData.fallback_chapter_id
        } else {
          updateChapter.fallbackChapterId = null
        }
        if (validationData.is_free_response) {
          updateChapter.isFreeResponse = validationData.is_free_response
          updateChapter.options = [{ value: 'free response' }]
        } else {
          updateChapter.isFreeResponse = validationData.is_free_response
          updateChapter.options = validationData.options ? validationData.options : null
        }
        if (audioSrc) {
          updateChapter.audioUrl = audioSrc
        } else if (validationData.is_audio_removed) {
          updateChapter.audioUrl = null
        }
        updateChapter.useTransaction(trx)
        await updateChapter.save()

        return updateChapter
      } catch (error) {
        console.log(error)
      }
    })

    return response.status(200).send({ success: true, data: result })
  }

  // public async updateOption({ request, response, params }: HttpContextContract) {
  //   const validateDataSchema = schema.create({
  //     option_one: schema.string.optional(),
  //     option_one_chapter_id: schema.number.optional([
  //       rules.exists({ table: 'chapters', column: 'id' }),
  //       rules.requiredIfExists('option_one'),
  //     ]),
  //     option_one_threshold: schema.number.optional(),
  //     option_two: schema.string.optional(),
  //     option_two_chapter_id: schema.number.optional([
  //       rules.exists({ table: 'chapters', column: 'id' }),
  //       rules.requiredIfExists('option_two'),
  //     ]),
  //     option_two_threshold: schema.number.optional(),
  //     option_three: schema.string.optional(),
  //     option_three_chapter_id: schema.number.optional([
  //       rules.exists({ table: 'chapters', column: 'id' }),
  //       rules.requiredIfExists('option_three'),
  //     ]),
  //     option_three_threshold: schema.number.optional(),
  //     option_four: schema.string.optional(),
  //     option_four_chapter_id: schema.number.optional([
  //       rules.exists({ table: 'chapters', column: 'id' }),
  //       rules.requiredIfExists('option_four'),
  //     ]),
  //     option_four_threshold: schema.number.optional(),
  //     option_five: schema.string.optional(),
  //     option_five_chapter_id: schema.number.optional([
  //       rules.exists({ table: 'chapters', column: 'id' }),
  //       rules.requiredIfExists('option_five'),
  //     ]),
  //     option_five_threshold: schema.number.optional(),
  //     option_six: schema.string.optional(),
  //     option_six_chapter_id: schema.number.optional([
  //       rules.exists({ table: 'chapters', column: 'id' }),
  //       rules.requiredIfExists('option_six'),
  //     ]),
  //     option_six_threshold: schema.number.optional(),
  //   })

  //   const validationData = await request.validate({
  //     schema: validateDataSchema,
  //   })

  //   const updateChapter = await Chapter.findOrFail(params.id)
  //   for (let key of Object.keys(validationData)) {
  //     updateChapter[camelCase(key)] = validationData[key]
  //   }

  //   await updateChapter.save()

  //   return response.status(200).send({ success: true, data: updateChapter })
  // }

  public async findOne({ params, response }: HttpContextContract) {
    const chapter = await Chapter.query().where('id', params.id).firstOrFail()

    return response.status(200).send({ data: chapter })
  }

  public async deleteChapter({ params, response }: HttpContextContract) {
    const deleteChapter = await Chapter.findOrFail(params.id)
    await deleteChapter.delete()

    return response.status(200).send({ success: true })
  }
}
