import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Setting from 'App/Models/Setting'
import { camelCase } from '@poppinss/utils/build/src/Helpers/string'
import _ from 'lodash'

export default class SettingsController {
  public async create({ request, response }: HttpContextContract) {
    const validateDataSchema = schema.create({
      name: schema.string(),
      description: schema.string(),
      handle: schema.string(),
      status: schema.string(),
      value: schema.string(),
      type: schema.string(),
    })

    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    const createSetting = await Setting.create({
      name: validateData.name,
      handle: validateData.handle,
      description: validateData.description,
      status: validateData.status,
      value: validateData.value,
      type: validateData.type,
    })

    return response.status(200).send({ success: true, data: createSetting })
  }

  public async update({ request, response, params }: HttpContextContract) {
    const validateDataSchema = schema.create({
      name: schema.string.optional(),
      handle: schema.string.optional(),
      description: schema.string.optional(),
      status: schema.string.optional(),
      value: schema.string.optional(),
      type: schema.string.optional(),
      metadata: schema.object.optional().anyMembers(),
    })

    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    const updateSetting = await Setting.findOrFail(params.id)
    for (let key of Object.keys(validateData)) {
      updateSetting[camelCase(key)] = validateData[key]
    }
    await updateSetting.save()

    return response.status(200).send({ success: true, data: updateSetting })
  }

  public async deleteSetting({ params, response }: HttpContextContract) {
    const deleteSetting = await Setting.findOrFail(params.id)
    await deleteSetting.delete()

    return response.status(200).send({ success: true })
  }

  public async findAll({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const allSetting = await Setting.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

    return response.status(200).send(allSetting)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const setting = await Setting.findOrFail(params.id)

    return response.status(200).send({ data: setting })
  }

  public async findByHandle({ response, params }: HttpContextContract) {
    const setting = await Setting.findBy('handle', params.handle)

    return response.status(200).send({ data: setting })
  }
}
