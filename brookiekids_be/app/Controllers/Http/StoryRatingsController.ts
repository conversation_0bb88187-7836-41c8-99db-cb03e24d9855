import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Story from 'App/Models/Story'
import StoryRating, { StoryRatingStatus } from 'App/Models/StoryRating'
import _ from 'lodash'

export default class StoryRatingsController {
  public async findStoryRatings({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
    const userFilters = { email: filters.user_email }
    const storyFilters = { id: filters.story_id }

    const storyRatings = await StoryRating.filter(filters)
      .where('status', StoryRatingStatus.RATED)
      .whereHas('user', (query) => query.filter(userFilters))
      .whereHas('story', (query) => query.filter(storyFilters))
      .preload('story')
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(storyRatings)
  }

  public async findMyStoryRating({ response, auth, params: { story_id } }: HttpContextContract) {
    try {
      const user = await auth.authenticate()

      const story = await Story.findOrFail(story_id)
      let findStoryRating = await StoryRating.query()
        .where('user_id', user.id)
        .where('story_id', story.id)
        .first()
      await Database.transaction(async (trx) => {
        if (findStoryRating) {
          const nextCount = findStoryRating.count + 1
          if (findStoryRating.status === StoryRatingStatus.ASKING && nextCount <= 4) {
            // nextCount = 2, 3 or 4
            if (nextCount <= 3) {
              // nextCount = 2, 3
              findStoryRating.count++
            } else {
              // nextCount = 4, but max count still remain 3
              findStoryRating.status = StoryRatingStatus.PENDING
            }
          }
        } else {
          findStoryRating = new StoryRating()
          findStoryRating.userId = user.id
          findStoryRating.storyId = story.id
          findStoryRating.count = 1 // initial count
          findStoryRating.status = StoryRatingStatus.ASKING
        }

        findStoryRating.useTransaction(trx)
        await findStoryRating.save()
      })

      return response.ok({ data: findStoryRating })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async updateMyStoryRating({
    request,
    response,
    params: { id },
    auth,
  }: HttpContextContract) {
    try {
      const user = await auth.authenticate()
      const validationSchema = schema.create({
        rating: schema.number.optional([rules.range(1, 5), rules.requiredIfNotExists('reason')]),
        review: schema.string.optional(),
        reason: schema.object.optional([rules.requiredIfNotExists('rating')]).members({
          selected_options: schema.array.optional().members(schema.string()),
          others: schema.string.optional(),
        }),
      })
      const validationData = await request.validate({ schema: validationSchema })

      const storyRating = await StoryRating.findOrFail(id)
      if (storyRating.userId !== user.id) {
        return response.badRequest({ success: false, message: 'Invalid access' })
      }

      await Database.transaction(async (trx) => {
        storyRating.merge({ ...validationData, status: StoryRatingStatus.RATED })
        storyRating.useTransaction(trx)
        await storyRating.save()
      })

      if (validationData.rating) {
        return response.ok({ success: true, data: { follow_up: validationData.rating <= 3 } })
      }

      return response.ok({ success: true })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
