import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Pack from 'App/Models/Pack'
import PackCode from 'App/Models/PackCode'
import Story from 'App/Models/Story'
import User from 'App/Models/User'
import UserPack from 'App/Models/UserPack'
import _, { keys } from 'lodash'
import { sift, sum } from 'radash'
import { uploadToS3Bucket } from './FilesController'
import Randomstring from 'randomstring'

export default class PacksController {
  public async findAll({ auth, request, response }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
    if (!keys(filters).includes('region')) {
      filters['region'] = 'sg'
    }

    // if logged in return user-preloaded packs
    // otherwise return all packs
    if (user == null) {
      const packs = await Pack.filter(filters)
        .preload('stories')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(packs)
    }

    const packs = await Pack.filter(filters)
      .preload('stories')
      .withCount('userPacks', (query) => query.where('user_id', user.id).as('redeemed'))
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(packs)
  }

  public async adminFind({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const packs = await Pack.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

    return response.status(200).send(packs)
  }

  public async adminFindOne({ params, response }: HttpContextContract) {
    const packs = await Pack.query()
      .where('id', params.id)
      .preload('packLevels', (query) => query.preload('file'))
      .first()

    return response.status(200).send(packs)
  }

  public async findMyPacks({ auth, request, response }: HttpContextContract) {
    const user = await auth.authenticate()
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    if (!keys(filters).includes('region')) {
      filters['region'] = user.region
    }

    const packs = await Pack.filter(filters)
      .select('packs.*')
      .leftJoin('user_packs', 'packs.id', 'user_packs.pack_id')
      .whereNotNull('user_packs.user_id')
      .whereNotNull('user_packs.pack_id')
      .where('user_packs.user_id', user.id)
      .withCount('userPacks', (query) => query.where('user_id', user.id).as('redeemed'))
      .preload('stories')
      .orderBy(`packs.${sort[0]}`, sort[1])
      .paginate(page, limit)

    return response.status(200).send(packs)
  }

  public async findOne({ auth, response, params }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)

    // if logged in return user-preloaded pack
    // otherwise return normal pack
    if (user == null) {
      const pack = await Pack.query()
        .where('id', params.id)
        .preload('packLevels', (query) => query.preload('file'))
        .first()
      return response.status(200).send({ data: pack })
    }

    const pack = await Pack.query()
      .where('id', params.id)
      .withCount('userPacks', (query) => query.where('user_id', user.id).as('redeemed'))
      .preload('packLevels')
      .first()

    return response.send({
      data: pack,
    })
  }

  public async findUserPacks({ auth, request, response }: HttpContextContract) {
    const user = await auth.authenticate()
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const userPack = await UserPack.filter(filters)
      .where('user_id', user.id)
      .preload('pack')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.send(userPack)
  }

  public async findUserPack({ auth, response, params }: HttpContextContract) {
    const user = await auth.authenticate()
    const pack = await Pack.query().where('id', params.id).preload('stories').firstOrFail()

    const userPack = await UserPack.query()
      .where('user_id', user.id)
      .where('pack_id', pack.id)
      .preload('pack', (query) => {
        query.preload('stories').preload('packLevels', (query) => query.preload('file'))
      })
      .preload(
        'user',
        (query) =>
          query.preload('children', (query) =>
            query.preload('sessions', (query) =>
              query
                .whereIn(
                  'story_id',
                  pack.stories.map((story) => story.id)
                )
                .preload('recordingResults', (query) => query.whereNotNull('hypothesis'))
            )
          )
        // query.withCount('recordingResults', (query) =>
        //   query
        //     .whereIn(
        //       'story_id',
        //       pack.stories.map((story) => story.id)
        //     )
        //     .whereNotNull('hypothesis')
        //     .preload('session', (query) => query.where('child_id', user.activeChildId))
        //     .as('attemptsToSpeak')
        // )
      )
      .first()

    if (userPack != null) {
      if ((userPack.storyStatuses?.length ?? 0) > 0) {
        // find wordCount by each child
        const children = userPack.user.children

        for await (const child of children) {
          const storyIds = userPack.storyStatuses.map((status) => {
            if (status.child_id == child.id) {
              return status.story_id
            }

            return undefined
          })

          const idList = sift(storyIds)

          // find all the stories
          const stories = await Story.query().whereIn('id', idList)

          const sumOfWords = sum(stories, (story) => story.wordCount ?? 0)

          child.$extras.wordsCount = sumOfWords
        }
      }
    }

    return response.send({
      data: userPack,
    })
  }

  public async redeemMyPack({ auth, request, response }: HttpContextContract) {
    const user = await auth.authenticate()
    const validationSchema = schema.create({
      code: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const isCodeValid = await PackCode.query()
      .where('pack_code', validationData.code)
      .whereNull('user_id')
      .first()

    if (!isCodeValid) {
      return response.status(400).send({ success: false, message: 'Invalid code' })
    }

    const isPackRedeemed = await UserPack.query()
      .where('user_id', user.id)
      .where('pack_id', isCodeValid.packId)
      .first()

    if (isPackRedeemed) {
      return response.status(400).send({
        success: true,
        message: 'Redeem failed. You already had this pack.',
      })
    }

    const packRedemption = await Database.transaction(async (trx) => {
      try {
        isCodeValid.userId = user.id
        isCodeValid.useTransaction(trx)
        await isCodeValid.save()

        const newPackRedemption = new UserPack()
        newPackRedemption.userId = user.id
        newPackRedemption.packId = isCodeValid.packId
        newPackRedemption.useTransaction(trx)
        await newPackRedemption.save()

        return newPackRedemption
      } catch (error) {
        console.log(error)
        await trx.rollback()
        return null
      }
    })

    if (packRedemption) {
      return response.status(200).send({ success: true, data: packRedemption })
    }

    return response.status(400).send({ success: false, message: 'Unable to redeeem.' })
  }

  public async updateUserPack({ auth, request, response, params }: HttpContextContract) {
    const user = await auth.authenticate()
    const validationSchema = schema.create({
      current_level: schema.number(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const userPack = await UserPack.query()
      .where('id', params.id)
      .where('user_id', user.id)
      .preload('pack', (query) => {
        query.preload('stories').preload('packLevels', (query) => query.preload('file'))
        // query.preload('packLevels', (query) => query.preload('file'))
      })
      .preload('user')
      .firstOrFail()

    userPack.merge({ currentLevel: validationData.current_level })
    await userPack.save()

    return response.status(200).send({ success: true, data: userPack })
  }

  public async updateLevelCompletedPrompt({
    request,
    response,
    auth,
    params,
  }: HttpContextContract) {
    try {
      const user = await User.findOrFail(auth.user?.id)
      const validationSchema = schema.create({
        level: schema.number(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      const findUserPack = await UserPack.query()
        .where('user_id', user.id)
        .where('pack_id', params.id)
        .firstOrFail()

      const currentLevelStatusList = findUserPack.levelStatuses ?? []
      const index = currentLevelStatusList.findIndex(
        (item) => item.level == validationData.level && item.child_id == user.activeChildId
      )

      if (index == -1) {
        return response.status(400).send({
          suceess: false,
          message: 'Invalid level or child id',
        })
      }

      currentLevelStatusList[index].triggered = true
      findUserPack.levelStatuses = currentLevelStatusList
      await findUserPack.save()

      return response.status(200).send({
        sucess: true,
        data: findUserPack,
      })
    } catch (error) {
      console.log(error)
    }
  }

  // public async updateMyPackRedemptionPlan({ auth, request, response }: HttpContextContract) {
  //   const user = await auth.authenticate()
  //   const validationSchema = schema.create({
  //     pack_id: schema.number([rules.exists({ table: 'packs', column: 'id' })]),
  //     plan_id: schema.number([rules.exists({ table: 'plans', column: 'id' })]),
  //   })

  //   const validationData = await request.validate({
  //     schema: validationSchema,
  //   })

  //   const pack = await Pack.findOrFail(validationData.pack_id)
  //   const packRedemption = await PackRedemption.query()
  //     .where('pack_id', pack.id)
  //     .where('user_id', user.id)
  //     .firstOrFail()

  //   // TODO: need to make sure plan exist in pack
  //   const plan = await Plan.findOrFail(validationData.plan_id)

  //   packRedemption.planId = plan.id
  //   await packRedemption.save()

  //   return response.status(200).send({ success: true, data: packRedemption })
  // }

  public async create({ request, response, auth }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)
    const validationSchema = schema.create({
      title: schema.string(),
      description: schema.string.optional(),
      thumbnail: schema.file.optional({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      region: schema.array().members(schema.string()),
      language: schema.string(),
      no_of_level: schema.number(),
    })
    const validationData = await request.validate({
      schema: validationSchema,
    })

    let src: string | null = null
    if (validationData.thumbnail) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      src = uploadBucket.url
    }

    const createPack = await Pack.create({
      title: validationData.title,
      description: validationData.description,
      region: validationData.region,
      language: validationData.language,
      noOfLevel: validationData.no_of_level,
      thumbnailUrl: src,
    })

    return response.status(200).send({ success: true, data: createPack })
  }

  public async update({ params, request, response, auth }: HttpContextContract) {
    console.log(request.all())
    const user = await User.findOrFail(auth.user?.id)
    const validationSchema = schema.create({
      title: schema.string([rules.trim()]),
      description: schema.string.optional(),
      thumbnail: schema.file.optional({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      is_thumbnail_removed: schema.boolean.optional(),
      region: schema.array().members(schema.string()),
      language: schema.string.optional(),
      no_of_level: schema.number.optional(),
      slides: schema.array.optional().members(
        schema.object().members({
          title: schema.string(),
          description: schema.string(),
          src: schema.string.optional(),
          image: schema.file.optional({
            extnames: ['png', 'jpg', 'jpeg'],
            size: '20mb',
          }),
        })
      ),
    })
    const validationData = await request.validate({
      schema: validationSchema,
    })
    console.log(validationData)

    let src: string
    if (validationData.thumbnail) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      src = uploadBucket.url
    }

    const updatePack = await Pack.findOrFail(params.id)
    const result = await Database.transaction(async (trx) => {
      if (validationData.title) {
        updatePack.title = validationData.title
      }
      if (validationData.description) {
        updatePack.description = validationData.description
      } else {
        updatePack.description = null
      }
      if (validationData.region) {
        updatePack.region = validationData.region
      }
      if (validationData.language) {
        updatePack.language = validationData.language
      }
      if (validationData.no_of_level) {
        updatePack.noOfLevel = validationData.no_of_level
      }
      if (src) {
        updatePack.thumbnailUrl = src
      } else if (validationData.is_thumbnail_removed) {
        updatePack.thumbnailUrl = null
      }

      updatePack.useTransaction(trx)
      await updatePack.save()
      return updatePack
    })

    return response.status(200).send({ success: true, data: result })
  }

  public async updatePackSlide({ params, request, response, auth }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)
    const validationSchema = schema.create({
      key: schema.string.optional(),
      title: schema.string([rules.trim()]),
      description: schema.string.optional(),
      mobile_thumbnail: schema.file.optional({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      is_mobile_thumbnail_removed: schema.boolean.optional(),
      tablet_thumbnail: schema.file.optional({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      is_tablet_thumbnail_removed: schema.boolean.optional(),
    })
    const validationData = await request.validate({
      schema: validationSchema,
    })

    let mobileSrc: string | undefined
    let tabletSrc: string | undefined
    if (validationData.mobile_thumbnail) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.mobile_thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      mobileSrc = uploadBucket.url
    }

    if (validationData.tablet_thumbnail) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.tablet_thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      tabletSrc = uploadBucket.url
    }

    const updatePack = await Pack.findOrFail(params.id)
    const slides = updatePack.slides ? [...updatePack.slides] : []
    let slide = {}
    let slideIndex = -1
    if (validationData.key) {
      slideIndex = slides.findIndex((s) => s.key == validationData.key)
      slide = slides[slideIndex]
    } else {
      let key = Randomstring.generate(6)
      slide['key'] = key
    }

    if (validationData.title) {
      slide['title'] = validationData.title
    }
    if (validationData.description) {
      slide['description'] = validationData.description
    } else {
      slide['description'] = undefined
    }
    if (mobileSrc) {
      slide['src_mobile'] = mobileSrc
    } else if (validationData.is_mobile_thumbnail_removed) {
      delete slide['src_mobile']
    }
    if (tabletSrc) {
      slide['src_tablet'] = tabletSrc
    } else if (validationData.is_tablet_thumbnail_removed) {
      delete slide['src_tablet']
    }

    if (slideIndex != -1) {
      slides[slideIndex] = slide
    } else {
      slides.push(slide)
    }

    const result = await Database.transaction(async (trx) => {
      updatePack.slides = slides
      updatePack.useTransaction(trx)
      await updatePack.save()
      return updatePack
    })

    return response.status(200).send({ success: true, data: result })
  }

  public async findPackByQrCode({ auth, response, params }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)

    // if logged in return user-preloaded pack
    // otherwise return normal pack
    if (user == null) {
      const pack = await Pack.query()
        .whereHas('stories', (query) => query.where('qr_code', params.qr_code))
        .preload('packLevels', (query) => query.preload('file'))
        .first()
      return response.status(200).send({ data: pack })
    }

    const pack = await Pack.query()
      .whereHas('stories', (query) => query.where('qr_code', params.qr_code))
      .withCount('userPacks', (query) => query.where('user_id', user.id).as('redeemed'))
      .preload('packLevels')
      .first()

    return response.send({
      data: pack,
    })
  }

  // public async addPlan({ request, response, params }: HttpContextContract) {
  //   const validationSchema = schema.create({
  //     plan_id: schema.number([rules.exists({ table: 'plans', column: 'id' })]),
  //   })

  //   const validationData = await request.validate({
  //     schema: validationSchema,
  //   })

  //   const pack = await Pack.findOrFail(params.id)

  //   // prevent duplicated activity
  //   const findPlan = await pack
  //     .related('plans')
  //     .query()
  //     .where('plan_id', validationData.plan_id)
  //     .first()

  //   if (!findPlan) {
  //     await pack.related('plans').attach([validationData.plan_id])
  //   }

  //   return response.status(200).send({ success: true })
  // }

  // public async deletePlan({ response, params }: HttpContextContract) {
  //   await Database.from('pack_plans')
  //     .where('pack_id', params.id)
  //     .where('plan_id', params.plan_id)
  //     .delete()

  //   return response.status(200).send({ success: true })
  // }
}
