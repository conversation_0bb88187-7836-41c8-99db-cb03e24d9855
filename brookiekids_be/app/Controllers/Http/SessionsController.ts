import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Session from 'App/Models/Session'
import Story, { StoryType } from 'App/Models/Story'
import StoryOrder from 'App/Models/StoryOrder'
import UserPack from 'App/Models/UserPack'
import { DateTime } from 'luxon'

export default class SessionsController {
  public async create({ auth, request, response }: HttpContextContract) {
    const user = await auth.authenticate()
    const validateDataSchema = schema.create({
      story_id: schema.number([rules.exists({ table: 'stories', column: 'id' })]),
    })

    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    const createSession = await Session.create({
      ...validateData,
      userId: user.id,
      childId: user.activeChildId,
    })

    return response.status(200).send({ success: true, data: createSession })
  }

  public async endSession({ auth, response, params }: HttpContextContract) {
    try {
      const user = await auth.authenticate()

      const findSession = await Session.query()
        .where('user_id', user.id)
        .where('id', params.id)
        .preload('story')
        .firstOrFail()

      findSession.endedAt = DateTime.now()
      await findSession.save()

      if (findSession.story.isCommunity || findSession.story.type !== StoryType.PACK) {
        // TODO: do we need to confirm user can create story order?
        const findStoryOrder = await StoryOrder.firstOrNew({
          userId: user.id,
          storyId: findSession.storyId,
        })

        // const findStoryOrder = await StoryOrder.query()
        //   .where('user_id', user.id)
        //   .where('story_id', findSession.storyId)
        //   .firstOrFail()

        const currentStatusList = findStoryOrder.storyStatuses ?? []
        const findIndex = currentStatusList.findIndex(
          (item) => item.child_id == user.activeChildId && item.story_id == findSession.storyId
        )
        if (findIndex == -1) {
          currentStatusList.push({
            story_id: findSession.storyId,
            child_id: user.activeChildId,
            completed: true,
          })
          findStoryOrder.storyStatuses = currentStatusList
          await findStoryOrder.save()
        }
      } else {
        const findUserPack = await UserPack.query()
          .where('user_id', user.id)
          .where('pack_id', findSession.story.packId)
          .firstOrFail()

        // update story status
        const currentStoryStatusList = findUserPack.storyStatuses ?? []
        const findStoryIndex = currentStoryStatusList.findIndex(
          (item) => item.child_id == user.activeChildId && item.story_id == findSession.storyId
        )
        if (findStoryIndex == -1) {
          currentStoryStatusList.push({
            story_id: findSession.storyId,
            child_id: user.activeChildId,
            completed: true,
          })
          findUserPack.storyStatuses = currentStoryStatusList
        }

        // update level status
        const currentLevelStatusList = findUserPack.levelStatuses ?? []
        const findLevelIndex = currentLevelStatusList.findIndex(
          (item) => item.child_id == user.activeChildId && item.level == findSession.story.level
        )

        if (findLevelIndex == -1) {
          const findPackLevelStories = await Story.query()
            .where('pack_id', findSession.story.packId)
            .where('level', findSession.story.level)

          const filteredChildStatusList = currentStoryStatusList.filter(
            (item) =>
              item.child_id === user.activeChildId &&
              findPackLevelStories.some((story) => story.id == item.story_id)
          )

          if (findPackLevelStories.length == filteredChildStatusList.length) {
            currentLevelStatusList.push({
              child_id: user.activeChildId,
              level: findSession.story.level,
              completed: true,
              triggered: false,
            })

            findUserPack.levelStatuses = currentLevelStatusList
          }
        }
        await findUserPack.save()
      }

      return response.status(200).send({ success: true, data: findSession })
    } catch (error) {
      console.log(error)
      return response.badRequest({ success: false, error: error.message ?? 'Server Error' })
    }
  }
}
