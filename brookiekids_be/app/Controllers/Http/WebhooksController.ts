import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { JwtPayload, verify } from 'jsonwebtoken'
import Event from '@ioc:Adonis/Core/Event'
import stripe from 'stripe'

export default class WebhooksController {
  public async wixCallback({ request, response }: HttpContextContract) {
    console.log('wix callbacks', request.all())

    try {
      const jwtToken = request.raw()
      const jwtPayload = verify(jwtToken as string, process.env.WIX_PUBLIC_KEY as string)
      if (!jwtPayload) {
        return response.status(400)
      }

      const eventData = JSON.parse((jwtPayload as JwtPayload).data)
      const webhookData = JSON.parse(JSON.parse((jwtPayload as JwtPayload).data).data)
      console.log('webhooks event', eventData)
      console.log('webhooks data', webhookData)

      // switch (eventData.eventType) {
      //   case 'wix.pricing_plans.plan_created':
      //     // Event.emit('plan:new', webhookData.createdEvent.entity)
      //     Event.emit('plan:new', webhookData)
      //     break
      //   case 'wix.pricing_plans.v2.order_purchased':
      //     Event.emit('plan:purchased', webhookData.actionEvent.body.order)
      //     break
      //   case 'wix.pricing_plans.v2.order_order-cycle-started':
      //     Event.emit('plan:cycle-started', webhookData.actionEvent.body.order)
      //     break
      //   case 'wix.pricing_plans.v2.order_canceled':
      //     Event.emit('plan:order-canceled', webhookData.actionEvent.body.order)
      //     break
      //   case 'wix.pricing_plans.v2.order_order-ended':
      //     Event.emit('plan:order-ended', webhookData.actionEvent.body.order)
      //     break
      //   default:
      //     // ToDo: double check
      //     // non plan related
      //     break
      // }

      if (webhookData.actionEvent.body.newFulfillmentStatus === 'FULFILLED') {
        Event.emit('webhook:wix', { email: webhookData.actionEvent.body.order.buyerInfo.email })
      }

      return response.status(200)
    } catch (error) {
      console.log('webhooks', error)
      return response.status(400).send({
        error,
        message: 'Failed to callback WIX',
      })
    }
  }

  public async stripeCallback({ request, response }: HttpContextContract) {
    // console.log('stripe callbacks', request.all())
    const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)
    const sig = request.headers()['stripe-signature']
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!

    let event

    try {
      event = stripeInstance.webhooks.constructEvent(request.raw() as string, sig!, endpointSecret)
    } catch (err) {
      console.log(err)
      return response.status(400).send(`Stripe webhook error: ${err.message}`)
    }

    console.log(event.type)

    try {
      // purchase webhook
      switch (event.type) {
        case 'customer.created':
          const customer = event.data.object
          Event.emit('stripe:customer-created', customer)

          break

        // case 'payment_method.attached':
        //   const paymentMethod = event.data.object
        //   console.log(`PaymentMethod ${paymentMethod.id} successfully created!`)
        //   // console.log(paymentMethod)
        //   // handlePaymentMethodCreated(paymentMethod);
        //   break

        case 'customer.updated':
          const updatedCustomer = event.data.object
          Event.emit('stripe:customer-updated', updatedCustomer)
          break

        // case 'charge.succeeded':
        //   const charge = event.data.object
        //   console.log(`Charge ${charge.id} successfully created!`)
        //   // console.log(charge)
        //   // handleChargeSucceeded(charge);
        //   break

        case 'customer.subscription.created':
          const subscription = event.data.object
          Event.emit('stripe:subscription-created', subscription)
          break

        // case 'payment_intent.created':
        //   const createdPaymentIntent = event.data.object
        //   console.log(`PaymentIntent ${createdPaymentIntent.id} successfully created!`)
        //   // console.log(createdPaymentIntent)
        //   // handlePaymentIntentCreated(createdPaymentIntent);
        //   break

        // case 'payment_intend.succeeded':
        //   const paymentIntent = event.data.object
        //   console.log(`PaymentIntent ${paymentIntent.id} successfully created!`)
        //   // console.log(paymentIntent)
        //   // handlePaymentIntentSucceeded(paymentIntent);
        //   break

        // case 'invoice.created':
        //   const invoice = event.data.object
        //   console.log(`Invoice ${invoice.id} successfully created!`)
        //   // console.log(invoice)
        //   // handleInvoiceCreated(invoice);
        //   break

        // case 'invoice.finalized':
        //   const finalizedInvoice = event.data.object
        //   console.log(`Invoice ${finalizedInvoice.id} finalized!`)
        //   // console.log(finalizedInvoice)
        //   // handleInvoiceFinalized(finalizedInvoice);
        //   break

        case 'invoice.paid':
          const paidInvoice = event.data.object
          Event.emit('stripe:plan-purchased', paidInvoice)
          break

        // case 'invoice.payment_succeeded':
        //   const paymentSucceededInvoice = event.data.object
        //   console.log(`Invoice ${paymentSucceededInvoice.id} payment succeeded!`)
        //   // console.log(paymentSucceededInvoice)
        //   // handleInvoicePaymentSucceeded(paymentSucceededInvoice);
        //   break

        case 'invoice.payment_failed':
          const paymentFailedInvoice = event.data.object
          console.log(`Invoice ${paymentFailedInvoice.id} payment failed!`)
          // console.log(paymentFailedInvoice)
          // handleInvoicePaymentFailed(paymentFailedInvoice);
          Event.emit('stripe:plan-failed', paymentFailedInvoice)
          break

        case 'customer.subscription.deleted':
          const deletedSubscription = event.data.object
          console.log(`Subscription ${deletedSubscription.id} successfully deleted!`)
          // console.log(deletedSubscription)
          Event.emit('stripe:subscription-deleted', deletedSubscription)
          break

        case 'customer.subscription.paused':
          const pausedSubscription = event.data.object
          console.log(`Subscription ${pausedSubscription.id} successfully paused!`)
          // console.log(pausedSubscription)
          // handleSubscriptionPaused(pausedSubscription);
          break

        case 'customer.subscription.resumed':
          const resumedSubscription = event.data.object
          console.log(`Subscription ${resumedSubscription.id} successfully resumed!`)
          // console.log(resumedSubscription)
          // handleSubscriptionResumed(resumedSubscription);
          break

        case 'customer.subscription.updated':
          const updatedSubscription = event.data.object
          Event.emit('stripe:subscription-updated', updatedSubscription)
          break

        case 'customer.subscription.trial_will_end':
          const trialWillEndSubscription = event.data.object
          console.log(`Subscription ${trialWillEndSubscription.id} trial will end!`)
          // console.log(trialWillEndSubscription)
          // handleSubscriptionTrialWillEnd(trialWillEndSubscription);
          break
      }

      // product webhook
      switch (event.type) {
        case 'price.created':
          const priceCreated = event.data.object
          Event.emit('stripe:price-created', priceCreated)
          break
        case 'price.updated':
          const priceUpdated = event.data.object
          Event.emit('stripe:price-updated', priceUpdated)
          break
        case 'price.deleted':
          const priceDeleted = event.data.object
          Event.emit('stripe:price-deleted', priceDeleted)
          break

        case 'product.created':
          const productCreated = event.data.object
          Event.emit('stripe:product-created', productCreated)
          break
        case 'product.updated':
          const productUpdated = event.data.object
          Event.emit('stripe:product-updated', productUpdated)
          break
        case 'product.deleted':
          const productDeleted = event.data.object
          Event.emit('stripe:product-deleted', productDeleted)
          break
      }

      return response.status(200)
    } catch (error) {
      console.log('webhooks', error)
      return response.status(400).send({
        error,
        message: 'Failed to callback stripe',
      })
    }
  }
}
