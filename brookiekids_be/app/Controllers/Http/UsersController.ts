import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import User from 'App/Models/User'
import _ from 'lodash'

export default class UsersController {
  public async find({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const users = await User.filter(filters)
      .doesntHave('admin')
      .preload('userPacks')
      .preload('subscriptions')
      .preload('devices')
      .preload('wallet', (query) =>
        query.withCount('transactions', (query) => query.sum('amount').as('balance'))
      )
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(users)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const user = await User.query()
      .where('id', params.id)
      .preload('children')
      .preload('userPacks')
      .preload('transactions')
      .firstOrFail()

    return response.status(200).send({
      data: user,
    })
  }

  public async findMyself({ auth, response }: HttpContextContract) {
    const myself = await auth.authenticate()

    // preload for other information
    const findMe = await User.query()
      .where('id', myself.id)
      .preload('children')
      .preload('userPacks')
      .preload('activeChild')
      .first()
    // .preload('userGroup')
    // .preload('userSetting')

    return response.status(200).send({
      data: findMe,
    })
  }

  public async updateUserInformation({ auth, request, response }: HttpContextContract) {
    const user = await auth.authenticate()
    const validateDataSchema = schema.create({
      region: schema.string.optional(),
      active_child_id: schema.number([
        rules.exists({ table: 'children', column: 'id', where: { user_id: user.id } }),
      ]),
      // profile: schema.object.optional().members({
      //   name: schema.string.optional(),
      //   phone: schema.string.optional({}, [rules.mobile({ strict: true })]),
      // }),
    })
    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    // if (validateData.profile) {
    //   for (let key of Object.keys(validateData.profile)) {
    //     user[_.camelCase(key)] = validateData.profile[key]
    //   }
    // }

    user.merge(validateData)
    await user.save()

    return response.status(200).send({
      success: true,
      data: user,
    })
  }

  // for admin use
  public async changePassword({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      user_id: schema.number([
        rules.exists({
          table: 'users',
          column: 'id',
        }),
      ]),
      password: schema.string({ trim: true }, [rules.required()]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const findUser = await User.find(validationData.user_id)
    if (findUser === null) {
      return response.send({
        success: false,
        message: 'Invalid user',
      })
    }

    findUser.password = validationData.password
    await findUser.save()

    return response.send({
      success: true,
    })
  }

  public async deleteMe({ auth, response }: HttpContextContract) {
    const user = await User.findOrFail(auth?.user?.id)
    user.blocked = true
    await user.save()
    await auth.logout()
    await Database.from('api_tokens').where('user_id', user.id).delete()
    return response.send({
      success: true,
    })
  }
}
