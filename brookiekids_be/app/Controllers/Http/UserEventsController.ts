import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import UserEvent from 'App/Models/UserEvent'
import _ from 'lodash'

export default class UserEventsController {
  public async findAll({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const userEvents = await UserEvent.filter(filters)
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(userEvents)
  }
}
