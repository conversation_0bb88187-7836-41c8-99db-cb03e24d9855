import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Version from 'App/Models/Version'

export default class VersionsController {
  public async create({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      platform: schema.string({ trim: true }, []),
      build_version: schema.string({ trim: true }, []),
      build_number: schema.number(),
      release_note: schema.string({ trim: true }),
      update: schema.boolean([]),
      app_url: schema.string({ trim: true }),
      published_at: schema.date(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    // const user = await User.findOrFail(auth?.user?.id)
    const createVersion = await Version.create({
      ...validationData,
    })
    return response.status(200).send({
      success: true,
      data: createVersion,
    })
  }

  public async update({ request, response, params }: HttpContextContract) {
    const validationSchema = schema.create({
      platform: schema.string({ trim: true }, []),
      build_version: schema.string({ trim: true }, []),
      build_number: schema.number(),
      release_note: schema.string({ trim: true }),
      update: schema.boolean([]),
      app_url: schema.string({ trim: true }),
      published_at: schema.date(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    console.log(validationData)
    // const user = await User.findOrFail(auth?.user?.id)
    const version = await Version.findByOrFail('id', params.id)
    version.merge({ ...validationData })
    await version.save()
    return response.status(200).send({
      success: true,
      data: version,
    })
  }

  public async batchDelete({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      ids: schema
        .array()
        .members(schema.number([rules.exists({ table: 'versions', column: 'id' })])),
    })
    const validationData = await request.validate({ schema: validationSchema })

    await Version.query().whereIn('id', validationData.ids).delete()
    return response.status(200).send({
      success: true,
    })
  }

  public async delete({ response, params }: HttpContextContract) {
    // const user = await User.findOrFail(auth?.user?.id)
    await Version.query().where('id', params.id).delete()
    return response.status(200).send({
      success: true,
    })
  }

  public async find({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const versions = await Version.query().orderBy(sort[0], sort[1]).paginate(page, limit)
    return response.send(versions)
  }

  public async findOne({ request, response, params }: HttpContextContract) {
    const version = await Version.filter(request.all()).where('id', params.id).first()
    return response.send({
      data: version,
    })
  }

  public async findLatestVersionPlatform({ response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      platform: schema.enum(['android', 'ios'] as const),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    const version = await Version.query()
      .where('platform', validationData.platform)
      .orderBy('published_at', 'desc')
      .first()
    return response.send({
      data: version,
    })
  }
}
