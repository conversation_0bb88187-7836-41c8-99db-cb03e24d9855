import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Tag from 'App/Models/Tag'

export default class TagsController {
  public async index({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)

    const tags = await Tag.query().where('status', 'active').orderBy('name', 'asc').paginate(page, limit)

    return response.ok(tags)
  }

  public async adminIndex({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const { status } = request.qs()

    const query = Tag.query()

    if (status) {
      query.where('status', status)
    }

    const tags = await query.orderBy('name', 'asc').paginate(page, limit)

    return response.ok(tags)
  }

  public async store({ request, response }: HttpContextContract) {
    const data = request.only(['name', 'status', 'image_url'])
    const tag = await Tag.create(data)

    return response.created({ tag })
  }

  public async update({ request, response, params }: HttpContextContract) {
    const tag = await Tag.findOrFail(params.id)
    const data = request.only(['name', 'status', 'image_url'])
    
    await tag.merge(data).save()

    return response.ok({ tag })
  }

  public async destroy({ response, params }: HttpContextContract) {
    const tag = await Tag.findOrFail(params.id)
    await tag.delete()

    return response.noContent()
  }
}
