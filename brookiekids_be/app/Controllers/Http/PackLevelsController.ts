import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import PackLevel from 'App/Models/PackLevel'
import { uploadToS3Bucket, uploadToS3BucketWithFileName } from './FilesController'
import User from 'App/Models/User'
import File from 'App/Models/File'

export default class PackLevelsController {
  public async updatePackLevelAudio({ request, response, auth, params }: HttpContextContract) {
    try {
      const user = await User.findOrFail(auth.user?.id)
      const validationSchema = schema.create({
        audio: schema.file({
          extnames: ['mp3'],
          size: '10mb',
        }),
      })

      const validationData = await request.validate({ schema: validationSchema })

      let src: string
      if (validationData.audio) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.audio,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        // console.log(uploadBucket)
        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload',
          })
        }

        src = uploadBucket.url
      }

      const findPackLevel = await PackLevel.findOrFail(params.id)

      await Database.transaction(async (trx) => {
        findPackLevel.audioUrl = src
        findPackLevel.useTransaction(trx)
        await findPackLevel.save()
      })

      return response.status(200).send({
        success: true,
      })
    } catch (error) {
      console.log(error)
    }
  }

  public async updatePackLevelRive({ request, response, auth, params }: HttpContextContract) {
    try {
      await auth.authenticate()
      const validationSchema = schema.create({
        rive: schema.file({
          extnames: ['riv'],
          size: '3gb',
        }),
      })

      const validationData = await request.validate({ schema: validationSchema })

      let src: string
      if (validationData.rive) {
        const uploadBucket = await uploadToS3BucketWithFileName(
          validationData.rive,
          process.env.S3_BUCKET_RIVE ?? 'hummusedu',
          '',
          validationData.rive.clientName
        )

        // console.log(uploadBucket)
        if (!uploadBucket?.url || !uploadBucket?.key) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload',
          })
        }

        src = process.env.S3_CLOUDFRONT_RIVE + uploadBucket.key
      }

      const findPackLevel = await PackLevel.findOrFail(params.id)

      await Database.transaction(async (trx) => {
        const newRive = new File()
        newRive.src = src
        newRive.type = 'rive'
        newRive.useTransaction(trx)
        await newRive.save()

        findPackLevel.fileId = newRive.id
        findPackLevel.useTransaction(trx)
        await findPackLevel.save()
      })

      return response.status(200).send({
        success: true,
      })
    } catch (error) {
      console.log(error)
    }
  }
}
