import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Bundle from 'App/Models/Bundle'
import BundleStory from 'App/Models/BundleStory'
import Transaction from 'App/Models/Transaction'
import User from 'App/Models/User'
import _ from 'lodash'

export default class BundlesController {
  public async findAll({ request, response, auth }: HttpContextContract) {
    try {
      const user = await User.find(auth.user?.id ?? 0)

      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      //   const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

      if (user == null) {
        const bundles = await Bundle.query()
          .where('blocked', false)
          .preload('stories', (query) => query.orderBy('id', 'desc'))
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)
        return response.ok(bundles)
      }

      const bundles = await Bundle.query()
        .where('blocked', false)
        .preload('stories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .orderBy('id', 'desc')
        )
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(bundles)
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async adminFind({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      //   const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

      const bundles = await Bundle.query()
        .preload('stories')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(bundles)
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async adminFindOne({ response, params: { id } }: HttpContextContract) {
    try {
      const bundle = await Bundle.query().where('id', id).preload('stories')

      return response.ok(bundle)
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async create({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        title: schema.string(),
        description: schema.string.optional(),
        region: schema.array().members(schema.string()),
        language: schema.string(),
        discount: schema.number(),
        blocked: schema.boolean(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      const result = await Database.transaction(async (trx) => {
        const newBundle = new Bundle()
        newBundle.title = validationData.title
        if (validationData.description) {
          newBundle.description = validationData.description
        }
        newBundle.region = validationData.region
        newBundle.language = validationData.language
        newBundle.discount = validationData.discount
        newBundle.blocked = validationData.blocked

        newBundle.useTransaction(trx)
        await newBundle.save()

        return newBundle
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    try {
      const bundle = await Bundle.findOrFail(id)
      const validationSchema = schema.create({
        title: schema.string(),
        description: schema.string.optional(),
        region: schema.array().members(schema.string()),
        language: schema.string(),
        discount: schema.number(),
        blocked: schema.boolean(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      const result = await Database.transaction(async (trx) => {
        bundle.title = validationData.title
        if (validationData.description) {
          bundle.description = validationData.description
        }
        bundle.region = validationData.region
        bundle.language = validationData.language
        bundle.discount = validationData.discount
        bundle.blocked = validationData.blocked

        bundle.useTransaction(trx)
        await bundle.save()

        return bundle
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async addStoriesToBundle({ request, response, params: { id } }: HttpContextContract) {
    const validationSchema = schema.create({
      story_ids: schema
        .array()
        .members(schema.number([rules.exists({ column: 'id', table: 'stories' })])),
    })
    const validationData = await request.validate({ schema: validationSchema })

    // ommit the existing story before attached
    validationData.story_ids.reverse().forEach(async (storyId, i) => {
      const isExists = await BundleStory.query()
        .where('bundle_id', id)
        .where('story_id', storyId)
        .first()
      if (isExists) {
        validationData.story_ids.splice(i, 1)
      }
    })

    const bundle = await Bundle.findOrFail(id)
    await Database.transaction(async (trx) => {
      await bundle.related('stories').attach(validationData.story_ids, trx)
    })

    return response.ok({ success: true })
  }

  public async removeStoryFromBundle({ response, params: { id, story_id } }: HttpContextContract) {
    const bundle = await Bundle.findOrFail(id)
    const bundleStory = await BundleStory.query()
      .where('bundle_id', bundle.id)
      .where('story_id', story_id)
      .first()

    if (!bundleStory) {
      return response.badRequest({ success: false, message: 'Invalid story in bundle.' })
    }

    await Database.transaction(async (trx) => {
      await bundle.related('stories').detach([bundleStory.storyId], trx)
    })

    return response.ok({ success: true })
  }

  public async findBundleRedemption({
    request,
    response,
    params: { bundle_id },
  }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const transactions = await Transaction.query()
      .where('title', `Redeemed Bundle (${bundle_id})`)
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(transactions)
  }
}
