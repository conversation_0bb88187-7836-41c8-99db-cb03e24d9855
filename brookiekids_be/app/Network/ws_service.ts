import ChatHistory from 'App/Models/ChatHistory'
import Chat from 'App/Models/Chat'
import User from 'App/Models/User'
import _ from 'lodash'
import WebSocket, { WebSocketServer } from 'ws'
// import customerServiceClient from './customerServiceClient'

export let chatListeners: {
  socket: WebSocket
}[] = []
export let historyListeners: {
  socket: WebSocket
  id: number
}[] = []

export const broadcastChat = async (chatId: string | number) => {
  const chat = await Chat.query().where('id', chatId).preload('user').first()
  if (!chat) return
  const json = chat.serialize()
  const data = stringToBuffer(JSON.stringify(json))
  chatListeners
    .filter((listener) => listener.socket['userId'] === chat.userId)
    .forEach((listener) => {
      if (listener.socket.readyState === WebSocket.OPEN) listener.socket.send(data)
    })
}

export const broadcastHistory = async (historyId: string | number) => {
  const history = await ChatHistory.query()
    .where('id', historyId)
    .preload('chat')
    .preload('user')
    .first()
  if (!history) return

  const json = history.serialize()
  const data = stringToBuffer(JSON.stringify(json))

  broadcastChat(history.chatId)

  historyListeners
    .filter(
      (listener) =>
        listener.id.toString() === history.chatId.toString() &&
        listener.socket['userId'] === history.chat.userId
    )
    .forEach((listener) => {
      if (listener.socket.readyState === WebSocket.OPEN) listener.socket.send(data)
    })
}

// string to bufferArray
export const stringToArrayBuffer = (str: string) =>
  new Uint8Array(str.split('').map((c) => c.charCodeAt(0))).buffer

// bufferArray to string
export const arrayBufferToString = (ab: ArrayBuffer) =>
  new Uint8Array(ab).reduce((p, c) => p + String.fromCharCode(c), '')

export const stringToBuffer = (str: string) => Buffer.from(str, 'utf-8')

class ChatWSS {
  public io: WebSocketServer
  private booted: boolean = false

  public boot() {
    if (this.booted) {
      return
    }

    this.booted = true
    this.io = new WebSocketServer({
      noServer: true,
    })
  }
}

export const ChatWs = new ChatWSS()

export const listenToChats = async (token: string, socket: WebSocket, request) => {
  const existingConnId = socket['connId']
  if (existingConnId) {
    stopListenToChats(existingConnId)
  }

  const consumerId = request.headers['x-consumer-id'] as string
  // const consumerName = request.headers['x-consumer-username']
  const user = await User.query().where('id', consumerId).first()

  if (!user) return

  socket['connId'] = token
  socket['userId'] = user.id

  chatListeners.push({
    socket: socket,
  })
}

export const stopListenToChats = (connId: string) => {
  chatListeners = chatListeners.filter((item) => {
    item.socket['connId'] !== connId
  })
}

export const listenToHistories = async (id: number, token: string, socket: WebSocket, request) => {
  const existingConnId = socket['connId']

  if (existingConnId) {
    stopListenToHistories(existingConnId)
  }

  const consumerId = request.headers['x-consumer-id'] as string
  // const consumerName = request.headers['x-consumer-username']
  const user = await User.query().where('id', consumerId).first()

  if (!user) return

  socket['connId'] = token
  socket['userId'] = user.id

  historyListeners.push({
    id: id,
    socket: socket,
  })
}

export const stopListenToHistories = (connId: string) => {
  historyListeners = historyListeners.filter((item) => {
    return item.socket['connId'] !== connId
  })
}
