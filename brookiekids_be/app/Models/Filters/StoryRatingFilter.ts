import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import StoryRating from 'App/Models/StoryRating'

export default class StoryRatingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof StoryRating, StoryRating>

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }
}
