import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import PackCode from 'App/Models/PackCode'

export default class PackCodeFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof PackCode, PackCode>

  public packCode(value: any): void {
    this.$query.where('pack_code', 'LIKE', `%${value}%`)
  }

  public pack(value: any): void {
    this.$query.where('pack_id', value)
  }

  public active(value: any): void {
    console.log(typeof value)
    if (value == '1') {
      this.$query.whereNull('user_id')
    } else if (value == '0') {
      this.$query.whereNotNull('user_id')
    }
  }
}
