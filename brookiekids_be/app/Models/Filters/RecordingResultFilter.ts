import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import RecordingResult from 'App/Models/RecordingResult'

export default class RecordingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof RecordingResult, RecordingResult>

  public email(value: any): void {
    this.$query.whereHas('user', (query) => query.where('email', 'LIKE', `%${value}%`))
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
