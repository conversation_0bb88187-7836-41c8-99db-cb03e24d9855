import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import StoryOrder from 'App/Models/StoryOrder'

export default class StoryOrderFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof StoryOrder, StoryOrder>

  public user(value: any): void {
    this.$query.where('user_id', value)
  }

  public preschool(value: any): void {
    this.$query.where('preschool_id', value)
  }
}
