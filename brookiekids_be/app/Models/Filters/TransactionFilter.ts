import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Transaction from 'App/Models/Transaction'

export default class TransactionFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Transaction, Transaction>

  public user (value: any): void {
    this.$query.where('user_id', value)
  }
}
