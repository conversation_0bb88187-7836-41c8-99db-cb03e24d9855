import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Admin from 'App/Models/Admin'

export default class AdminFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Admin, Admin>

  public type(value: any): void {
    this.$query.where('type', value)
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }
}
