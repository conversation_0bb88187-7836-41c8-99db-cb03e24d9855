import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Child from 'App/Models/Child'

export default class ChildFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Child, Child>

  public name(value: any): void {
    this.$query.where('name', 'LIKE', `%${value}%`)
  }

  public userId(value: any): void {
    this.$query.where('user_id', value)
  }
}
