import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Plan from 'App/Models/Plan'

export default class PlanFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Plan, Plan>

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }

  public region(value: any): void {
    this.$query.where('region', 'LIKE', `%${value}%`)
  }

  public language(value: any): void {
    this.$query.where('language', value)
  }

  public published(value: any): void {
    this.$query.where('published', value === 'true')
  }
}
