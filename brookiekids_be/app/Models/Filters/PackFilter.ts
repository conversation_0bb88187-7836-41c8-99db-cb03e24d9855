import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Pack from 'App/Models/Pack'

export default class PackFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Pack, Pack>

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }

  public region(value: any): void {
    this.$query.where('region', 'LIKE', `%${value}%`)
  }

  public language(value: any): void {
    this.$query.where('language', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
