import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Story from 'App/Models/Story'

export default class StoryFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Story, Story>

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }

  public region(value: any): void {
    this.$query.where('region', 'LIKE', `%${value}%`)
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public language(value: any): void {
    this.$query.where('language', value)
  }

  public isCommunity(value: any): void {
    this.$query.where('is_community', value === 'true' ? true : false)
  }

  public preschool(value: any): void {
    this.$query.where('preschool_id', value)
  }

  public plan(value: any): void {
    this.$query.where('plan_id', value)
  }

  public bundle(value: any): void {
    this.$query.whereHas('bundleStory', (query) => query.where('bundle_id', value))
  }

  public isFeatured(value: any): void {
    this.$query.where('is_featured', value === 'true' ? true : false)
  }

  public status(value: any): void {
    this.$query.whereIn('status', value)
  }

  public excludeRedeemed(value: any): void {
    this.$query.whereNot((query) => {
      query.whereHas('storyOrders', (query) => {
        query.where('user_id', value)
      })
    })
  }

  public type(value: any): void {
    this.$query.whereIn('type', value)
  }
}
