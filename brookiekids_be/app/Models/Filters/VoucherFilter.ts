import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Voucher from 'App/Models/Voucher'

export default class VoucherFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Voucher, Voucher>

  public voucherCode(value: any): void {
    this.$query.where('voucher_code', 'LIKE', `%${value}%`)
  }

  public story(value: any): void {
    this.$query.where('story_id', Number(value))
  }
}
