import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Feedback from 'App/Models/Feedback'

export default class FeedbackFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Feedback, Feedback>

  public message(value: any): void {
    this.$query.where('message', 'LIKE', `%${value}%`)
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
