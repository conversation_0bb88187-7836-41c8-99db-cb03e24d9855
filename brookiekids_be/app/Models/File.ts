import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class File extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public position: number

  @column()
  public alt: string

  @column()
  public width: number

  @column()
  public height: number

  @column()
  public src: string | null

  @column()
  public thumbnailUrl: string

  @column()
  public type: string
  //file, image, video, recording

  @column()
  public ext: string

  @column()
  public userId: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
