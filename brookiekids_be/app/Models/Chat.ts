import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  belongsTo,
  column,
  computed,
  HasMany,
  hasMany,
  beforeFind,
  beforeFetch,
  ModelQueryBuilderContract,
} from '@ioc:Adonis/Lucid/Orm'
import ChatHistory from './ChatHistory'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import Database from '@ioc:Adonis/Lucid/Database'
import User from './User'

export default class Chat extends compose(BaseModel, Filterable) {
  @column({ isPrimary: true })
  public id: number

  @column()
  public firstName?: string

  @column()
  public lastName?: string

  @column()
  public userId: number

  @belongsTo(() => User, { foreignKey: 'userId' })
  public user: BelongsTo<typeof User>

  @hasMany(() => ChatHistory, {
    foreignKey: 'chatId',
  })
  public histories: Has<PERSON>any<typeof ChatHistory>

  @computed()
  public get latest_message() {
    if (this.$preloaded['histories'] && this.histories.length > 0) {
      return this.histories[0].serialize()
    }
    return null
  }

  @computed()
  public get latest_message_date() {
    return this.$extras['latest_message_date'] ?? null
  }

  @computed()
  public get total_unread() {
    return this.$extras['total_unread'] ?? null
  }

  // @column.dateTime()
  // public expiresAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeFind()
  public static getLatestHistorySingle(query: ModelQueryBuilderContract<typeof Chat>) {
    query
      .select(
        Database.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .max('chat_histories.created_at')
          .as('latest_message_date')
      )
      .select(
        Database.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .whereNull('seen')
          .andWhere('is_deleted', false)
          .count('*')
          .as('total_unread')
      )
      .select('chats.*')
      .preload('histories', (query) => {
        query.where('is_deleted', false).groupOrderBy('created_at', 'desc')
      })
  }

  @beforeFetch()
  public static getLatestHistoryMultiple(query: ModelQueryBuilderContract<typeof Chat>) {
    query
      .select(
        Database.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .max('chat_histories.created_at')
          .as('latest_message_date')
      )
      .select(
        Database.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .whereNull('seen')
          .andWhere('is_deleted', false)
          .count('*')
          .as('total_unread')
      )
      .select('chats.*')
      .preload('histories', (query) => {
        query.where('is_deleted', false).groupOrderBy('created_at', 'desc').groupLimit(1)
      })
  }
}
