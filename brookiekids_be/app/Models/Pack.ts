import { DateTime } from 'luxon'
import {
  Base<PERSON>odel,
  Has<PERSON>any,
  ManyToMany,
  column,
  computed,
  hasMany,
  manyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import PackFilter from './Filters/PackFilter'
import User from './User'
import Story from './Story'
import PackLevel from './PackLevel'

export default class Pack extends compose(BaseModel, Filterable) {
  public static $filter = () => PackFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  public description: string | null

  @column()
  public thumbnailUrl: string | null

  @column()
  public featuredImage: string

  @column({ prepare: (value) => JSON.stringify(value) })
  public region: string | Array<string>

  @column()
  public language: string

  @column()
  public noOfLevel: number

  @column()
  public store_url: string

  @column({ prepare: (value) => JSON.stringify(value) })
  public slides: Array<any> | string

  @hasMany(() => Story, {
    serializeAs: 'stories',
  })
  public stories: HasMany<typeof Story>

  @hasMany(() => PackLevel, {
    serializeAs: 'pack_levels',
  })
  public packLevels: HasMany<typeof PackLevel>

  @manyToMany(() => User, {
    pivotTable: 'user_packs',
    pivotColumns: ['current_level'],
    pivotForeignKey: 'pack_id',
    pivotRelatedForeignKey: 'user_id',
    pivotTimestamps: true,
  })
  public userPacks: ManyToMany<typeof User>

  @computed({ serializeAs: 'has_redeemed' })
  public get hasRedeemed() {
    return this.$extras.redeemed > 0 ? true : false
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
