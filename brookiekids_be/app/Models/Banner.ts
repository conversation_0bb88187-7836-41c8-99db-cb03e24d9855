import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import BannerFilter from './Filters/BannerFilter'

export default class Banner extends compose(BaseModel, Filterable) {
  public static $filter = () => BannerFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public thumbnailUrl: string

  @column()
  public ctaUrl: string

  @column()
  public region: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
