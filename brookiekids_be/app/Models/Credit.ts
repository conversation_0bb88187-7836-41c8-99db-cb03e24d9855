import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column } from '@ioc:Adonis/Lucid/Orm'
import _ from 'lodash'

export default class Credit extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  public description: string

  @column()
  public handle: string

  @column({ consume: (value: number) => Number(value) })
  public amount: number

  @column({ consume: (value: number) => Number(value) })
  public price: number // current price

  @column({ consume: (value: number) => Number(value) })
  public compareAt: number // original price

  @column()
  public currency: string

  @column()
  public rank: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static updateField(credit: Credit) {
    if (credit.$dirty.handle == null) {
      credit.handle = _.snakeCase(`top-up ${credit.amount} ${credit.currency}`)
    }
  }
}
