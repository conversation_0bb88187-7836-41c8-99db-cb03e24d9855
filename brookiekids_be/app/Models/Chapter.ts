import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { BaseModel, belongsTo, BelongsTo, column, computed } from '@ioc:Adonis/Lucid/Orm'
import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'
import Story from './Story'
import ChapterFilter from './Filters/ChapterFilter'
import File from './File'

export type Option = {
  value: string
  threshold?: number
  chapterId?: number
  answer?: boolean
  // chapter?: Chapter
}

export default class Chapter extends compose(BaseModel, Filterable) {
  public static $filter = () => ChapterFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  @slugify({
    strategy: 'dbIncrement',
    fields: ['title'],
    allowUpdates: true,
  })
  public handle: string

  @column()
  public description: string

  @column()
  public storyId: number

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isFreeResponse: boolean

  @column()
  public audioUrl: string | null

  @column()
  public fileId: number

  @belongsTo(() => File, {
    foreignKey: 'fileId',
    serializeAs: 'video',
  })
  public video: BelongsTo<typeof File>

  @column({ prepare: (value) => JSON.stringify(value) })
  public options: Array<Option> | string | null

  @column({ serializeAs: 'fallback_chapter_id' })
  public fallbackChapterId: number | null

  @belongsTo(() => Chapter, {
    foreignKey: 'fallbackChapterId',
    serializeAs: 'fallback_chapter',
  })
  public fallbackChapter: BelongsTo<typeof Chapter>

  @computed({ serializeAs: 'is_last_video' })
  public get isLastVideo() {
    return (this.options?.length ?? 0) <= 0 ?? true
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
