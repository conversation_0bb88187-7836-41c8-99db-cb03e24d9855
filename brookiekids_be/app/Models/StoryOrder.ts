import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Voucher from './Voucher'
import Story from './Story'
import User from './User'
import { StoryStatus } from './UserPack'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import StoryOrderFilter from './Filters/StoryOrderFilter'

export default class StoryOrder extends compose(BaseModel, Filterable) {
  public static $filter = () => StoryOrderFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public voucherId: number

  @belongsTo(() => Voucher)
  public voucher: BelongsTo<typeof Voucher>

  @column()
  public storyId: number

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column({ consume: (value: number) => Number(value) })
  public finalAmount: number

  @column({ consume: (value: number) => Number(value) })
  public discountAmount: number

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  public storyStatuses: Array<StoryStatus>

  @column({ consume: (value: boolean) => Boolean(value) })
  public blocked: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
