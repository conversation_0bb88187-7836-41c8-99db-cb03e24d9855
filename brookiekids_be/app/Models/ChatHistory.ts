import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import {
  BaseModel,
  column,
  belongsTo,
  BelongsTo,
  beforeFetch,
  ModelQueryBuilderContract,
  beforePaginate,
} from '@ioc:Adonis/Lucid/Orm'
import Chat from './Chat'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import User from './User'

export default class ChatHistory extends compose(BaseModel, Filterable) {
  @column({ isPrimary: true })
  public id: number

  @column()
  public chatId: number

  @column()
  public userId: number

  @belongsTo(() => User, { foreignKey: 'userId' })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Chat, { foreignKey: 'chatId' })
  public chat: BelongsTo<typeof Chat>

  @column({ serialize: (value) => !!value })
  public isDeleted: boolean

  @column.dateTime()
  public seen: DateTime

  @column()
  public content: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforePaginate()
  public static ignoreDeleteMultiplePaginate(
    quaries: ModelQueryBuilderContract<typeof ChatHistory>[]
  ) {
    quaries[0].andWhere('is_deleted', false)
    quaries[1].andWhere('is_deleted', false)
  }

  @beforeFetch()
  public static ignoreDeleteMultiple(query: ModelQueryBuilderContract<typeof ChatHistory>) {
    query.andWhere('is_deleted', false)
  }
}
