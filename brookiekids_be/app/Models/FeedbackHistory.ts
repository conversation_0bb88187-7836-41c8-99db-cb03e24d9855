import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Feedback from './Feedback'
import User from './User'

export default class FeedbackHistory extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public content: string

  @column()
  public toUserId: number

  @belongsTo(() => User, { foreignKey: 'to_user_id' })
  public toUser: BelongsTo<typeof User>

  @column()
  public fromUserId: number

  @belongsTo(() => User, { foreignKey: 'from_user_id' })
  public fromUser: BelongsTo<typeof User>

  @column()
  public feedbackId: number

  @belongsTo(() => Feedback)
  public feedback: BelongsTo<typeof Feedback>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
