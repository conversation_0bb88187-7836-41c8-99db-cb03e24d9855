import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Plan from './Plan'
import PlanPricing from './PlanPricing'

export default class Subscription extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public planId: number

  @belongsTo(() => Plan)
  public plan: BelongsTo<typeof Plan>

  @column()
  public planPricingId: number

  @belongsTo(() => PlanPricing)
  public planPricing: BelongsTo<typeof PlanPricing>

  @column()
  public provider: string
  // google
  // apple
  // stripe

  @column()
  public providerPlanId: string

  @column()
  public providerSubscriptionId: string

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column()
  public providerUserId: string

  @column.dateTime()
  public startDate: DateTime
  // stripe: current_period_start

  @column.dateTime()
  public endDate: DateTime | null | undefined
  // actual end date
  // TODO: on ended webhook from stripe

  @column.dateTime()
  public cycleStartDate: DateTime
  // stripe: current_period_start

  @column.dateTime()
  public cycleEndDate: DateTime
  // stripe: current_period_end

  @column()
  public status: string
  // stripe status: incomplete, trialing, active, past_due, canceled, unpaid

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
