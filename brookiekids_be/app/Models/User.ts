import { DateTime } from 'luxon'
import Hash from '@ioc:Adonis/Core/Hash'
import {
  column,
  beforeSave,
  BaseModel,
  belongsTo,
  BelongsTo,
  hasMany,
  HasMany,
  hasOne,
  HasOne,
  manyToMany,
  ManyToMany,
  beforeCreate,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import Child from './Child'
import UserGroup from './UserGroup'
import UserSetting from './UserSetting'
import UserFilter from './Filters/UserFilter'
import Pack from './Pack'
import File from './File'
import Session from './Session'
import Admin from './Admin'
import RecordingResult from './RecordingResult'
import Device from './Device'
import PurchaseClickTracker from './PurchaseClickTracker'
import StoryOrder from './StoryOrder'
import Wallet from './Wallet'
import Transaction from './Transaction'
import Subscription from './Subscription'

export const generateReferralCode = (length: number = 6) => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 1; i <= length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export default class User extends compose(BaseModel, Filterable) {
  public static $filter = () => UserFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public stripeCustomerId: string

  @column()
  public email: string

  @column()
  public region: string

  @column({ serializeAs: null })
  public password: string

  @column()
  public referralCode: string

  @column()
  public name: string

  @column()
  public phone: string

  @column({ columnName: 'address_1', serializeAs: 'address_1' })
  public address1: string

  @column({ columnName: 'address_2', serializeAs: 'address_2' })
  public address2: string

  @column()
  public postcode: string

  @column()
  public rememberMeToken: string | null

  // anonymous for generateed users
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isAnonymous: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public verified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public phoneVerified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public blocked?: boolean

  @column()
  public userGroupId: number | null

  @belongsTo(() => UserGroup)
  public userGroup: BelongsTo<typeof UserGroup>

  @column()
  public activeChildId: number

  @hasOne(() => Child)
  public activeChild: HasOne<typeof Child>

  @hasMany(() => Child)
  public children: HasMany<typeof Child>

  @hasMany(() => RecordingResult)
  public recordingResults: HasMany<typeof RecordingResult>

  @hasOne(() => UserSetting)
  public userSetting: HasOne<typeof UserSetting>

  @hasMany(() => Session)
  public sessions: HasMany<typeof Session>

  @hasOne(() => Session)
  public lastSession: HasOne<typeof Session>

  @hasOne(() => Admin)
  public admin: HasOne<typeof Admin>

  @hasMany(() => StoryOrder)
  public storyOrders: HasMany<typeof StoryOrder>

  @hasMany(() => Subscription)
  public subscriptions: HasMany<typeof Subscription>

  @hasMany(() => PurchaseClickTracker, { serializeAs: 'purchase_clicks' })
  public purchaseClickTrackers: HasMany<typeof PurchaseClickTracker>

  @hasMany(() => Device)
  public devices: HasMany<typeof Device>

  // @column.dateTime()
  // public lastLoginAt: DateTime

  @column.dateTime()
  public promotionEndedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => Pack, {
    pivotTable: 'user_packs',
    pivotColumns: ['current_level'],
    pivotForeignKey: 'user_id',
    pivotRelatedForeignKey: 'pack_id',
    pivotTimestamps: true,
    serializeAs: 'packs',
  })
  public userPacks: ManyToMany<typeof Pack>

  @hasMany(() => File)
  public files: HasMany<typeof File>

  @beforeSave()
  public static async hashPassword(user: User) {
    if (user.$dirty.password) {
      user.password = await Hash.make(user.password)
    }
  }

  @beforeCreate()
  public static async referral(user: User) {
    if (!user.$dirty.referralCode) {
      let repeat = false
      let referralCode
      do {
        referralCode = generateReferralCode(8)
        if (await User.findBy('referral_code', referralCode)) {
          repeat = true
        } else {
          repeat = false
        }
      } while (repeat || !referralCode)
      user.referralCode = referralCode
    }
  }

  public static async verifyPassword(oldPassword: string, hashedPassword: string) {
    const checkPassword = await Hash.verify(hashedPassword, oldPassword)
    if (checkPassword) {
      return true
    } else {
      return false
    }
  }

  @hasOne(() => Wallet)
  public wallet: HasOne<typeof Wallet>

  @hasMany(() => Transaction)
  public transactions: HasMany<typeof Transaction>
}
