import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, HasMany, belongsTo, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import User from './User'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import ChildFilter from './Filters/ChildFilter'
import Session from './Session'
import { computed } from '@ioc:Adonis/Lucid/Orm'

export default class Child extends compose(BaseModel, Filterable) {
  public static $filter = () => ChildFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column.dateTime()
  public birthdate: DateTime

  @column()
  public englishLevel: number

  @column()
  public chineseLevel: number

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => Session)
  public sessions: HasMany<typeof Session>

  @computed({ serializeAs: 'attempts_to_speak' })
  public get attemptsToSpeak() {
    if (!this.sessions) {
      return 0
    }

    return this.sessions.reduce((accum, cur) => (accum += cur?.recordingResults?.length ?? 0), 0)
  }

  @computed({ serializeAs: 'words_count' })
  public get wordsCount() {
    return this.$extras.wordsCount ?? 0
  }
}
