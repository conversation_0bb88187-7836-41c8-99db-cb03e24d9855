import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import UserEventFilter from './Filters/UserEventFilter'

export default class UserEvent extends compose(BaseModel, Filterable) {
  public static $filter = () => UserEventFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public type: string
  // login, etc

  @column()
  public location: string

  @column()
  public ipAddress: string

  @column()
  public device: string

  @column()
  public provider: string

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
