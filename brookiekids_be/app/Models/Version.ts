import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import VersionFilter from './Filters/VersionFilter'

export default class Version extends compose(BaseModel, Filterable) {
  public static $filter = () => VersionFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public platform: string

  @column()
  public buildVersion: string

  @column()
  public buildNumber: number

  @column()
  public releaseNote: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public update: boolean

  @column()
  public appUrl: string

  @column.dateTime()
  public publishedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
