import { DateTime } from 'luxon'
import { BaseModel, column, computed, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import User from './User'
import UserGroupFilter from './Filters/UserGroupFilter'

// to group users to different country/market
export default class UserGroup extends compose(BaseModel, Filterable) {
  public static $filter = () => UserGroupFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  // one default group to assign to users
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isDefault: boolean

  @hasMany(() => User)
  public users: HasMany<typeof User>

  @computed({ serializeAs: 'users_count' })
  public get usersCount() {
    if (this.$extras.users_count !== null) {
      return this.$extras.users_count
    }

    return null
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
