import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  beforeCreate,
  beforeUpdate,
  belongsTo,
  column,
} from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Story from './Story'

export default class PurchaseClickTracker extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @column()
  public storyId: number

  @column()
  public clicks: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async createClick(entry: PurchaseClickTracker) {
    if (!entry.$dirty.clicks) {
      entry.clicks = 1
    }
  }

  @beforeUpdate()
  public static async updateClick(entry: PurchaseClickTracker) {
    if (!entry.$dirty.clicks) {
      entry.clicks = entry.clicks + 1
    }
  }
}
