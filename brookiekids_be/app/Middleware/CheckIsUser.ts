import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'

export default class CheckIsAdmin {
  public async handle({ request, response }: HttpContextContract, next: () => Promise<void>) {
    try {
      const user = await User.query()
        .where('email', request.input('email'))
        .doesntHave('admin')
        .first()

      if (!user) {
        return response.status(400).send({
          success: false,
          message: 'Invalid Account',
        })
      }

      console.log('next')
      // code for middleware goes here. ABOVE THE NEXT CALL
      await next()
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        message: 'Invalid Account',
      })
    }
  }
}
