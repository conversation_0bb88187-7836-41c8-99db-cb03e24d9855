import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class CheckIsAdmin {
  public async handle({ request, response }: HttpContextContract, next: () => Promise<void>) {
    try {
      const inputPassword = request.input('password')
      if (inputPassword !== 'Gaincue') {
        return response.status(400).send({
          success: false,
          message: 'UNAUTHORIZED ACCESS',
        })
      }

      // code for middleware goes here. ABOVE THE NEXT CALL
      await next()
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        message: 'UNAUTHORIZED ACCESS',
      })
    }
  }
}
