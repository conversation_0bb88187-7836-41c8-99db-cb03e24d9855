import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class RequireLogin {
  public async handle({ auth, response }: HttpContextContract, next: () => Promise<void>) {
    try {
      // code for middleware goes here. ABOVE THE NEXT CALL
      const isLogin = await auth.check()

      if (!isLogin) {
        return response.status(400).send({
          success: false,
          message: "Please login before proceeding to in app-purchase"
        })
      }

      await next()
    } catch (error) {
      console.log(error)
    }
  }
}
