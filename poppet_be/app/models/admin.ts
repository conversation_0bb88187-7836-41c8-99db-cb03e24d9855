import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@adonisjs/lucid/orm'

export default class <PERSON><PERSON> extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare type: number

  //crud
  //0-r,1-rc,2-rcu,3-rcud
  @column()
  declare accessKyc: number

  @column()
  declare accessFunding: number

  @column()
  declare accessUser: number

  @column()
  declare accessReport: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => User, {})
  // declare user: BelongsTo<typeof User>
}
