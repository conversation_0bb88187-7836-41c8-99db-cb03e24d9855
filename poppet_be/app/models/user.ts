import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import {
  BaseModel,
  column,
  beforeSave,
  beforeCreate,
  belongsTo,
  BelongsTo,
  hasMany,
  HasMany,
  hasOne,
  HasOne,
  manyToMany,
  ManyToMany
} from '@adonisjs/lucid/orm'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import { DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email'],
  passwordColumnName: 'password',
})

export const generateReferralCode = (length: number = 6) => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 1; i <= length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export default class User extends compose(BaseModel, AuthFinder) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare stripeCustomerId: string

  @column()
  declare email: string

  @column()
  declare region: string

  @column({ serializeAs: null })
  declare password: string

  @column()
  declare referralCode: string

  @column()
  declare name: string

  @column()
  declare phone: string

  @column({ columnName: 'address_1', serializeAs: 'address_1' })
  declare address1: string

  @column({ columnName: 'address_2', serializeAs: 'address_2' })
  declare address2: string

  @column()
  declare postcode: string

  @column()
  declare rememberMeToken: string | null

  // anonymous for generated users
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isAnonymous: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare verified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare phoneVerified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare blocked?: boolean

  @column()
  declare userGroupId: number | null

  @column()
  declare activeChildId: number

  @column.dateTime()
  declare promotionEndedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => UserGroup)
  // declare userGroup: BelongsTo<typeof UserGroup>

  // @hasOne(() => Child)
  // declare activeChild: HasOne<typeof Child>

  // @hasMany(() => Child)
  // declare children: HasMany<typeof Child>

  // @hasMany(() => RecordingResult)
  // declare recordingResults: HasMany<typeof RecordingResult>

  // @hasOne(() => UserSetting)
  // declare userSetting: HasOne<typeof UserSetting>

  // @hasMany(() => Session)
  // declare sessions: HasMany<typeof Session>

  // @hasOne(() => Session)
  // declare lastSession: HasOne<typeof Session>

  // @hasOne(() => Admin)
  // declare admin: HasOne<typeof Admin>

  // @hasMany(() => StoryOrder)
  // declare storyOrders: HasMany<typeof StoryOrder>

  // @hasMany(() => Subscription)
  // declare subscriptions: HasMany<typeof Subscription>

  // @hasMany(() => PurchaseClickTracker, { serializeAs: 'purchase_clicks' })
  // declare purchaseClickTrackers: HasMany<typeof PurchaseClickTracker>

  // @hasMany(() => Device)
  // declare devices: HasMany<typeof Device>

  // @manyToMany(() => Pack, {
  //   pivotTable: 'user_packs',
  //   pivotColumns: ['current_level'],
  //   pivotForeignKey: 'user_id',
  //   pivotRelatedForeignKey: 'pack_id',
  //   pivotTimestamps: true,
  //   serializeAs: 'packs',
  // })
  // declare userPacks: ManyToMany<typeof Pack>

  // @hasMany(() => File)
  // declare files: HasMany<typeof File>

  // @hasOne(() => Wallet)
  // declare wallet: HasOne<typeof Wallet>

  // @hasMany(() => Transaction)
  // declare transactions: HasMany<typeof Transaction>

  // Hooks
  @beforeSave()
  static async hashPassword(user: User) {
    if (user.$dirty.password) {
      user.password = await hash.make(user.password)
    }
  }

  @beforeCreate()
  static async generateReferral(user: User) {
    if (!user.$dirty.referralCode) {
      let repeat = false
      let referralCode
      do {
        referralCode = generateReferralCode(8)
        if (await User.findBy('referral_code', referralCode)) {
          repeat = true
        } else {
          repeat = false
        }
      } while (repeat || !referralCode)
      user.referralCode = referralCode
    }
  }

  // Static methods
  static async verifyPassword(oldPassword: string, hashedPassword: string) {
    const checkPassword = await hash.verify(hashedPassword, oldPassword)
    return checkPassword
  }

  static accessTokens = DbAccessTokensProvider.forModel(User)
}
