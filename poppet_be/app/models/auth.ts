import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

// for email & sms verification
export default class Auth extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare email: string

  @column()
  declare phone: string

  @column()
  declare code: string

  @column()
  declare status: number
  //0-new 1-used

  @column()
  declare type: number
  //0-register,1-forgot_password,2-phone

  @column()
  declare userId: number

  @column.dateTime()
  declare expiredAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
