import { DateTime } from 'luxon'
import { BaseModel, column, computed, hasMany, HasMany } from '@adonisjs/lucid/orm'

// to group users to different country/market
export default class UserGroup extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  // one default group to assign to users
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isDefault: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @hasMany(() => User)
  // declare users: HasMany<typeof User>

  @computed({ serializeAs: 'users_count' })
  get usersCount() {
    if (this.$extras.users_count !== null) {
      return this.$extras.users_count
    }
    return null
  }
}
