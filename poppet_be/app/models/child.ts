import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, HasMany, belongsTo, column, hasMany, computed } from '@adonisjs/lucid/orm'

export default class Child extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column.dateTime()
  declare birthdate: DateTime

  @column()
  declare englishLevel: number

  @column()
  declare chineseLevel: number

  @column()
  declare userId: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => User)
  // declare user: BelongsTo<typeof User>

  // @hasMany(() => Session)
  // declare sessions: HasMany<typeof Session>

  @computed({ serializeAs: 'attempts_to_speak' })
  get attemptsToSpeak() {
    // Note: This will need to be updated once Session model is migrated
    // if (!this.sessions) {
    //   return 0
    // }
    // return this.sessions.reduce((accum, cur) => (accum += cur?.recordingResults?.length ?? 0), 0)
    return 0
  }

  @computed({ serializeAs: 'words_count' })
  get wordsCount() {
    return this.$extras.wordsCount ?? 0
  }
}
